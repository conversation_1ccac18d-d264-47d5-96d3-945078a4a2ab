{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "lint": "eslint ."}, "dependencies": {"@gorhom/bottom-sheet": "^4.4.7", "@invertase/react-native-apple-authentication": "^2.2.2", "@react-native-async-storage/async-storage": "^1.17.6", "@react-native-camera-roll/camera-roll": "^7.8.3", "@react-native-clipboard/clipboard": "^1.8.5", "@react-native-community/datetimepicker": "^4.0.1", "@react-native-community/masked-view": "^0.1.11", "@react-native-community/netinfo": "^7.1.3", "@react-native-community/slider": "^4.1.12", "@react-native-firebase/app": "^15.7.0", "@react-native-firebase/dynamic-links": "^15.7.0", "@react-native-firebase/messaging": "^15.7.0", "@react-native-google-signin/google-signin": "^7.2.2", "@react-native/metro-config": "^0.78.0", "@react-navigation/bottom-tabs": "^6.0.9", "@react-navigation/drawer": "^6.1.8", "@react-navigation/native": "^6.0.6", "@react-navigation/native-stack": "^6.2.5", "@react-navigation/stack": "^6.0.11", "@reduxjs/toolkit": "^1.6.1", "html-entities": "^2.3.2", "lottie-react-native": "^5.1.5", "react": "18.2.0", "react-content-loader": "^6.1.0", "react-dom": "18.2.0", "react-native": "0.73.6", "react-native-actions-sheet": "^0.5.8", "react-native-camera": "^4.2.1", "react-native-compressor": "^1.6.1", "react-native-element-dropdown": "^1.8.0", "react-native-fast-image": "^8.5.11", "react-native-flash-message": "^0.4.1", "react-native-fs": "^2.19.0", "react-native-gesture-handler": "^2.14.0", "react-native-image-crop-picker": "^0.41.0", "react-native-image-picker": "^4.7.1", "react-native-linear-gradient": "^2.8.3", "react-native-notifier": "^1.8.0", "react-native-orientation-locker": "^1.7.0", "react-native-pager-view": "^5.4.9", "react-native-paper": "^4.10.1", "react-native-permissions": "^3.6.1", "react-native-popover-view": "^5.1.8", "react-native-razorpay": "^2.3.0", "react-native-reanimated": "^3.16.7", "react-native-reanimated-carousel": "^3.1.1", "react-native-redash": "^16.2.3", "react-native-render-html": "^6.3.4", "react-native-safe-area-context": "^3.3.2", "react-native-screenguard": "^0.2.2", "react-native-screens": "^3.10.0", "react-native-share": "^7.3.3", "react-native-simple-dialogs": "^1.4.0", "react-native-sound": "^0.11.2", "react-native-svg": "^12.1.1", "react-native-swipe-gestures": "^1.0.5", "react-native-tab-view": "^3.1.1", "react-native-vector-icons": "^9.2.0", "react-native-video": "^6.0.0-alpha.7", "react-native-webview": "^11.23.1", "react-redux": "^7.2.6", "react-test-renderer": "^18.2.0", "rn-fetch-blob": "^0.12.0"}, "devDependencies": {"@babel/core": "^7.26.10", "@babel/runtime": "^7.26.10", "@react-native-community/eslint-config": "2.0.0", "babel-jest": "26.6.3", "babel-plugin-module-resolver": "^5.0.2", "eslint": "7.14.0", "hermes-engine": "^0.11.0", "jest": "^26.6.3", "metro-config": "^0.81.1", "metro-react-native-babel-preset": "^0.77.0", "metro-react-native-babel-transformer": "^0.77.0", "react-devtools": "^6.1.1", "react-native-asset": "^2.1.1", "react-native-svg-transformer": "^1.5.0"}, "jest": {"preset": "react-native"}}