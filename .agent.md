# SoTrue Mobile Project - Developer Guide

## Project Overview

- **Name**: SoTrue Mobile App
- **Type**: React Native mobile application
- **Version**: 0.0.1
- **Platform**: Cross-platform (iOS & Android)
- **Language**: JavaScript/JSX with some TypeScript
- **Framework**: React Native 0.73.6

## Purpose
SoTrue appears to be a social media platform focused on video content sharing, stories, posts, and user interactions. The app includes features like:
- Video content creation and sharing
- User profiles and following system
- Stories functionality
- Comments and reactions
- Subscription/payment system
- Real-time notifications

## Key Technologies & Dependencies

### Core Framework
- React Native 0.73.6
- React 18.2.0
- React Navigation 6.x (<PERSON>ack, Bottom Tabs, Drawer)
- Redux Toolkit for state management

### UI & Animation
- React Native Paper (Material Design)
- React Native Reanimated 3.x
- Lottie React Native
- React Native Linear Gradient
- React Native Vector Icons

### Media & Camera
- React Native Camera
- React Native Video
- React Native Image Picker/Crop Picker
- React Native Fast Image
- React Native Compressor

### Firebase & Authentication
- Firebase (App, Messaging, Dynamic Links)
- Google Sign-In
- Apple Authentication

### Other Key Libraries
- React Native Gesture Handler
- React Native Safe Area Context
- React Native Permissions
- React Native Share
- React Native Sound
- Razorpay (payments)

## Project Structure

### Core Directories

#### `/components/`
Reusable UI components organized by feature:
- `common/` - Shared components (buttons, inputs, dialogs, etc.)
- `post/` - Post-related components
- `profile/` - Profile-related components
- `story/` - Story functionality components
- `login/` - Authentication components
- `notifications/` - Notification components
- `playlist/` - Media playlist components

#### `/screens/`
Screen components for navigation:
- Authentication screens (Login, Signup, etc.)
- Main app screens (Home, Profile, Settings, etc.)
- Feature-specific screens (Camera, Comments, etc.)
- `2.0/` - Newer version screens

#### `/navigation/`
Navigation configuration:
- `AppNavigator.js` - Main navigation stack
- `BottomNavigator.js` - Bottom tab navigation
- `HomeDrawerNavigator.js` - Drawer navigation

#### `/constants/`
App-wide constants:
- `Colors.js` - Theme colors and color schemes
- `Constants.js` - App constants and configuration
- `Dimensions.js` - Layout dimensions
- `Fonts.js` - Font configurations

#### `/theme/`
Theme management:
- Custom theme provider and hooks
- Responsive design utilities
- Dark/light mode support

#### `/utils/`
Utility functions:
- `ServerConnector.js` - API communication
- `Utils.js` - General utility functions
- `AuthLogin.js` - Authentication utilities
- `PermissionManager.js` - Device permissions

#### `/store/`
Redux store configuration:
- `store.js` - Redux store setup
- `slice/` - Redux slices

#### `/data/`
Data management:
- `Data.js` - App data and state
- `MetaData.js` - Metadata configurations

#### `/assets/`
Static assets:
- Images, videos, audio files
- Reaction animations and sounds

## Development Best Practices

### Code Organization
- Use functional components with hooks
- Implement proper component separation by feature
- Follow React Native naming conventions
- Use TypeScript for new components when possible

### Styling
- Use the custom theme system (`useSTheme` hook)
- Leverage the color constants from `constants/Colors.js`
- Implement responsive design using theme utilities
- Support both dark and light themes

### State Management
- Use Redux Toolkit for global state
- Use local state for component-specific data
- Implement proper state normalization

### Navigation
- Use React Navigation 6.x patterns
- Implement deep linking support
- Handle navigation state properly

### Performance
- Use `react-native-fast-image` for image optimization
- Implement proper list virtualization
- Use React.memo for expensive components
- Optimize video playback and media handling

### API Integration
- Use `ServerConnector.js` for API calls
- Implement proper error handling
- Use async/await patterns
- Handle network connectivity states

## Configuration Files

### Build Configuration
- `metro.config.js` - Metro bundler configuration
- `babel.config.js` - Babel transpilation setup
- `react-native.config.js` - React Native CLI configuration

### Platform-Specific
- `android/app/build.gradle` - Android build configuration
- `ios/Podfile` - iOS dependencies and configuration

### Development Tools
- `.eslintrc.js` - ESLint configuration
- `.prettierrc.js` - Code formatting rules
- `jest` configuration in package.json

## Key Features Implementation

### Authentication
- Multiple sign-in methods (Google, Apple, Email)
- OTP verification system
- Password reset functionality

### Media Handling
- Camera integration for photo/video capture
- Video compression and optimization
- Image cropping and editing
- Audio playback for reactions

### Social Features
- User following/followers system
- Post creation and sharing
- Story functionality
- Comments and reactions
- User blocking/restricting

### Monetization
- Subscription system
- Payment integration (Razorpay)
- Referral system

## Development Commands

```bash
# Start Metro bundler
npm start

# Run on Android
npm run android

# Run on iOS
npm run ios

# Run tests
npm test

# Lint code
npm run lint
```

## Important Notes

### Permissions
The app requires various permissions:
- Camera access
- Photo library access
- Microphone access
- Storage access
- Location access (optional)

### Firebase Setup
- Ensure proper Firebase configuration
- Configure push notifications
- Set up dynamic links

### Platform Considerations
- iOS: Hermes is disabled in Podfile
- Android: Uses Kotlin and Google Services
- Both platforms support vector icons and custom fonts

### Theme System
- Custom theme provider with dark/light mode support
- Color theming system with multiple color schemes
- Responsive font and dimension utilities

## Troubleshooting

### Common Issues
- Ensure all native dependencies are properly linked
- Check Firebase configuration for push notifications
- Verify camera and storage permissions
- Clear Metro cache if experiencing bundling issues

### Performance Optimization
- Use FlatList for large data sets
- Implement proper image caching
- Optimize video playback settings
- Monitor memory usage for media-heavy screens