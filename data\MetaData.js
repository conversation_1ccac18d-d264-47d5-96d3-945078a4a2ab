import * as AngerIcon from '../assets/reactions/jsonfile/angry.json';
import * as Irritated<PERSON>ottie from '../assets/reactions/jsonfile/angry.json';
import * as BoredBeforeLottie from '../assets/reactions/jsonfile/bored before.json';
import * as HappyAfterLottie from '../assets/reactions/jsonfile/happy after.json';
import * as HeartbrokenBeforeLottie from '../assets/reactions/jsonfile/heartbroken before.json';
import * as HmmLottie from '../assets/reactions/jsonfile/hmm.json';
import * as LoveBeforeLottie from '../assets/reactions/jsonfile/love before.json';
import * as NostalgiaLottie from '../assets/reactions/jsonfile/nostalgia.json';
import * as ScaredAfterLottie from '../assets/reactions/jsonfile/scared before.json';
import * as SureLottie from '../assets/reactions/jsonfile/sure.json';
import * as WowLottie from '../assets/reactions/jsonfile/wow.json';

export const REACTION_LIST = [
  {value: 'ANGER', label: 'Anger', icon: AngerIcon},
  // { value: "AUD", label: "Audience", icon: require("../assets/Images/icon/reactions/Audience.png") },
  {value: 'BOO', label: 'Boo', icon: AngerIcon},
  {value: 'BLADY', label: 'Bosslady', icon: AngerIcon},
  {value: 'BMAN', label: 'BossMan', icon: AngerIcon},
  {value: 'CELEB', label: 'Celebration', icon: AngerIcon},
  {value: 'CLAP', label: 'Clap', icon: AngerIcon},
  {value: 'DISG', label: 'Disgust', icon: AngerIcon},
  {value: 'FEAR', label: 'Fear', icon: AngerIcon},
  {value: 'MEH', label: 'Meh', icon: AngerIcon},
  {value: 'LIT', label: 'Potty', icon: AngerIcon},
  {value: 'SURP', label: 'Surprise', icon: AngerIcon},
  {value: 'TEASE', label: 'Tease', icon: AngerIcon},
  {value: 'TPASS', label: 'Timepass', icon: AngerIcon},
  {value: 'WEVER', label: 'Whatever', icon: AngerIcon},
  {value: 'YAY', label: 'Yay', icon: AngerIcon},
  {value: 'YUM', label: 'Yum', icon: AngerIcon},

  // { value: "LIKE", label: "Luv", icon: require("../assets/Images/icon/reactions/Luv.png") },
];

export const HARDCODED_EMOTIONS = [
  {
    value: 'ANGRY',
    label: 'Angry',
    icon: AngerIcon,
    lottiePath: IrritatedLottie,
    audioPath: 'anger.mp3',
    vibrationPattern: [0, 150, 40, 150, 40, 150], // 530ms
    isChecked: false,
    count: 42, // Hardcoded count for demo
  },
  {
    value: 'BORED',
    label: 'Bored',
    icon: AngerIcon, // Placeholder
    lottiePath: BoredBeforeLottie,
    audioPath: 'bored.mp3',
    vibrationPattern: [0, 40, 300, 40, 300, 40], // 720ms
    isChecked: false,
    count: 18, // Hardcoded count for demo
  },
  {
    value: 'HAPPY',
    label: 'Happy',
    icon: AngerIcon, // Placeholder
    lottiePath: HappyAfterLottie,
    audioPath: 'happy.mp3',
    vibrationPattern: [0, 60, 40, 60, 40, 60, 40, 60], // 360ms (Joy)
    isChecked: false,
    count: 127, // Hardcoded count for demo
  },
  {
    value: 'HEARTBROKEN',
    label: 'Heartbroken',
    icon: AngerIcon, // Placeholder
    lottiePath: HeartbrokenBeforeLottie,
    audioPath: 'heartbroken_before_2_1.mp3',
    vibrationPattern: [0, 60, 250, 60, 250, 60], // 680ms (Sadness)
    isChecked: false,
    count: 9, // Hardcoded count for demo
  },
  {
    value: 'HMM',
    label: 'Hmm',
    icon: AngerIcon, // Placeholder
    lottiePath: HmmLottie,
    audioPath: 'hmm_1.mp3',
    vibrationPattern: [0, 30, 50, 30, 50, 50, 50, 150], // 410ms (Anticipation)
    isChecked: false,
    count: 73, // Hardcoded count for demo
  },
  {
    value: 'LOVED',
    label: 'Loved',
    icon: AngerIcon, // Placeholder
    lottiePath: LoveBeforeLottie,
    audioPath: 'love_before_2.mp3',
    vibrationPattern: [0, 100, 200, 100, 300, 100], // 800ms (Love)
    isChecked: false,
    count: 256, // Hardcoded count for demo
  },
  {
    value: 'NOSTALGIA',
    label: 'Nostalgia',
    icon: AngerIcon, // Placeholder
    lottiePath: NostalgiaLottie,
    audioPath: 'nostalgia_2_1.mp3',
    vibrationPattern: [0, 60, 100, 60, 200, 60], // 480ms
    isChecked: false,
    count: 34, // Hardcoded count for demo
  },
  {
    value: 'SCARED',
    label: 'Scared',
    icon: AngerIcon, // Placeholder
    lottiePath: ScaredAfterLottie,
    audioPath: 'scared_after_2_1.mp3',
    vibrationPattern: [0, 70, 30, 120, 30, 120, 30, 120], // 520ms (Fear)
    isChecked: false,
    count: 15, // Hardcoded count for demo
  },
  {
    value: 'SURE',
    label: 'Sure',
    icon: AngerIcon, // Placeholder
    lottiePath: SureLottie,
    audioPath: 'sure.mp3',
    vibrationPattern: [0, 40, 50, 40, 50, 200, 40], // 420ms (Compliments)
    isChecked: false,
    count: 89, // Hardcoded count for demo
  },
  {
    value: 'WOW',
    label: 'Wow',
    icon: AngerIcon, // Placeholder
    lottiePath: WowLottie,
    audioPath: 'wow.mp3',
    vibrationPattern: [0, 100, 50, 200, 50, 200], // 600ms (Awe)
    isChecked: false,
    count: 156, // Hardcoded count for demo
  },
];
