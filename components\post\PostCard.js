import React, {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import {
  Image,
  StyleSheet,
  View,
  TouchableOpacity,
  ScrollView,
  Pressable,
  ImageBackground,
  Platform,
  TextInput,
  Text,
  StatusBar,
  Keyboard,
  Modal,
  Alert,
  Vibration,
  Animated,
} from 'react-native';
import {AppStateContext, SinglePostContext} from '../..';
import EntutoTextView from '../common/EntutoTextView';
import ProgressiveImage from '../common/ProgressiveImage';
import LinearGradient from 'react-native-linear-gradient';
import ActionSheet from 'react-native-actions-sheet';
import UnlockPostActionView from './UnlockPostActionView';
import ThreeDotMenuActionView from './ThreeDotMenuActionView';
import {
  checkValueLength,
  creationOfCopyLink,
  decodeHtmlEntitessData,
  formatLikeNumber,
  getValueFromReactions,
  hasImageUrlExist,
  preventDoubleClick,
} from '../../utils/Utils';
import LikeBtnComponent from '../common/LikeBtnComponent';
import BookmarkBtnComponent from '../common/BookmarkBtnComponent';
import ProfileImagePlaceholder from '../../assets/Images/full_user_image_place_holder.png';
import ThreeDotIcon from '../../assets/Images/icon/three_dot.png';
import ThreeDotVerticalIcon from '../../assets/Images/icon/profile_three_dot.png';
import PlayBtnIcon from '../../assets/Images/icon/play_btn.png';
import PlayBtnIconFilled from '../../assets/Images/icon/Play_Icon_Filled.png';

import LockIcon from '../../assets/Images/icon/post_lock.png';
import VerifiedIcon from '../../assets/Images/icon/verifiedicon.png';
import ServerConnector from '../../utils/ServerConnector';
import {
  CurrencySymbol,
  TAGGED_SYMBOL,
  UserHandlePrefix,
  _RedirectionErrorList,
} from '../../utils/Appconfig';
import {RedirectionUrlFunction} from '../../utils/RedirectionUrl';
import BottomSheetSuccessMsg from '../common/BottomSheetSuccessMsg';
import ConfirmationPopup from '../common/ConfirmationPopup';
import appData from '../../data/Data';
import ErrorMessages from '../../constants/ErrorMessages';
import Colors from '../../constants/Colors';
import CustomSnackbar from '../common/CustomSnackbar';
import TagProfileIcon from '../../assets/Images/icon/post_tag_icon.png';
import DescriptionCaptionStyle from '../common/DescriptionCaptionStyle';
import {TapGestureHandler} from 'react-native-gesture-handler';
import {
  FadeIn,
  FadeInDown,
  FadeOutUp,
  runOnJS,
  useAnimatedStyle,
  useSharedValue,
  withDelay,
  withSpring,
  withTiming,
} from 'react-native-reanimated';
import HeartActive from '../../assets/Images/icon/double_tap.png';
import HeartVideoActive from '../../assets/Images/icon/double_tap.png';
import SHARE_ICON from '../../assets/Images/icon/share_icon.png';
import Dimensions from '../../constants/Dimensions';

import LIKE_ICON from '../../assets/Images/icon/like_icon.png';
import BOOKMARK_ICON from '../../assets/Images/icon/bookmark.png';
import BOOKMARK_FILLED_ICON from '../../assets/Images/icon/bookmark-filled.png';

import COMMENT_ICON from '../../assets/Images/icon/comment.png';

import PlayViewCount from '../../assets/Images/icon/play_view_icon.png';
import Video from 'react-native-video';
import CommentComponent from './CommentComponent';
import {
  BottomSheetFooter,
  BottomSheetModal,
  BottomSheetModalProvider,
  BottomSheetScrollView,
} from '@gorhom/bottom-sheet';
import {ActivityIndicator} from 'react-native-paper';
import {
  useSafeAreaFrame,
  useSafeAreaInsets,
} from 'react-native-safe-area-context';
import Share from 'react-native-share';
import SharePostProfileFeature from '../common/SharePostProfileFeature';
import SharePostIcon from '../common/SharePostIcon';
import {
  _getFirstTimeLikePost,
  _getFirstTimeUnlockPost,
  _setFirstTimeLikePost,
  _setFirstTimeUnlockPost,
} from '../../utils/AuthLogin';
import UpdateUserLocationComponent from '../profile/UpdateUserLocationComponent';
import useDefaultStyle from '../../theme/useDefaultStyle';
import useSThemedStyles from '../../theme/useSThemedStyles';
import useSTheme from '../../theme/useSTheme';
import ResolutionVideoSwitch from './ResolutionVideoSwitch';
import ReelProgressBar from '../videoContents/ReelProgressBar';
import {
  REACTION_LIKE,
  REACTION_LIT,
  REACTION_MEH,
} from '../../constants/Constants';
import PlaylistPlaceholder from '../../assets/Images/full_user_image_place_holder.png';
import TempData from '../../data/TempData';
import FullScreenPlayer from './FullScreenPlayer';
import LottieView from 'lottie-react-native';
import Sound from 'react-native-sound';
import {HARDCODED_EMOTIONS} from '../../data/MetaData';
import {Dimensions as RNDimensions} from 'react-native';

const AnimatedImage = Animated.createAnimatedComponent(Image);
const IMAGE_HEIGHT = Dimensions.screenWidth - 40;

const PostCard = ({
  itemData,
  navigation,
  isMyProfile,
  isForceUnlock = false,
  postCardType = '',
  forceBookmark = false,
  showBookmarkWarrings = false,
  postCardClick,
  showVideoContent = false,
  fullScreen = false,
  rowIndex = 0,
  currentVisibleIndex = 0,
  repeatVideo = false,
  showSharePopup = false,
  cameFrom = '',

  ...props
}) => {
  const {
    fullUserDetails,
    __commentObj,
    showCommentCountV,
    changeShowCommentCount,
    homepagePostDataBackup,
  } = useContext(AppStateContext);
  const {changeSingleProfileObj} = useContext(SinglePostContext);
  const {defaultStyle} = useDefaultStyle();
  const theme = useSTheme();
  const style = useSThemedStyles(styles);

  let __has_state_city = fullUserDetails.hasOwnProperty('_has_state_city')
    ? fullUserDetails._has_state_city
    : 'NO';
  const __profile_seq = fullUserDetails.hasOwnProperty('_profile_seq')
    ? fullUserDetails._profile_seq
    : -1;
  useEffect(() => {
    __has_state_city = fullUserDetails.hasOwnProperty('_has_state_city')
      ? fullUserDetails._has_state_city
      : 'NO';
  }, [fullUserDetails]);

  const insets = useSafeAreaInsets();
  const frame = useSafeAreaFrame();
  const BottomGap = Platform.OS == 'ios' ? 10 : insets.bottom;
  const AVL_HEIGHT = frame.height - BottomGap; // For top dipsplay minus -insets.top
  const AVL_WIDTH = frame.width;
  const BOTTOM_ACTION_BOX_GAP = Platform.OS == 'ios' ? insets.bottom + 18 : 30;

  const PostUnlockMsg = 'UNLOCK POST FOR ';
  const ProfileUnlockMsg = 'UNLOCK ALL POSTS FOR ';
  const [postIsLike, setPostIsLike] = useState(false);
  const [likeBtnDisable, setlikeBtnDisable] = useState(true);
  const [postIsBookmark, setpostIsBookmark] = useState(false);
  const [bookmarkBtnDisable, setbookmarkBtnDisable] = useState(true);
  const [commentBtnDisable, setcommentBtnDisable] = useState(true);
  const [postIsSubs, setpostIsSubs] = useState(false);
  const [unlockPost, setunlockPost] = useState(false);
  const [unlockMsg, setunlockMsg] = useState('');
  const [unlockFees, setunlockFees] = useState(0);
  const [unlockFeesValue, setunlockFeesValue] = useState(0);
  const [perMonthTxt, setperMonthTxt] = useState('');
  const [isPaidProfile, setisPaidProfile] = useState(false);
  const [likeCount, setlikeCount] = useState(0);
  const [showCommentCount, setShowCommentCount] = useState(false);

  const postSeq = itemData.post_seq;

  const [blockPost, setblockPost] = useState(false);
  const [blockMsg, setblockMsg] = useState('');
  const [blockMsgDB, setblockMsgDB] = useState('');

  const [showConfirmPopup, setshowConfirmPopup] = useState(false);
  const [showConfirmPopupKey, setshowConfirmPopupKey] = useState(Math.random());

  const [confirmTitle, setconfirmTitle] = useState('Confirmation');
  const [confirmMsg, setconfirmMsg] = useState('Confirmation');
  const [warringsData, setwarringsData] = useState({});

  const [postComments, setpostComments] = useState('');
  const expiryPostDate = itemData.expire_on;

  const [SnackbarMsg, setSnackbarMsg] = useState('');
  const [displaySnackbar, setdisplaySnackbar] = useState(false);
  const [refreshSnackBar, setrefreshSnackBar] = useState(Math.random());
  const [snackBarType, setsnackBarType] = useState('FAILED');

  const [showCommentActive, setshowCommentActive] = useState(false);
  const [showBookmarkIcon, setshowBookmarkIcon] = useState(true);

  const [showBMWarrings, setshowBMWarrings] = useState(false);

  const [showTagUser, setshowTagUser] = useState(false);
  const [validUserHandleList, setvalidUserHandleList] = useState([]);
  const [cardType, setcardType] = useState('');

  const scale = useSharedValue(0);
  const opacity = useSharedValue(1);
  const doubleTapRef = useRef();

  const scaleVideo = useSharedValue(0);
  const opacityVideo = useSharedValue(1);
  const doubleTapVideoRef = useRef();
  const [playVideo, setPlayVideo] = useState(true);
  const [isServiceExecute, setIsServiceExecute] = useState(false);
  const videoPlayerRef = useRef(null);

  const [commentCount, setCommentCount] = useState(0);
  const [videoLoading, setVideoLoading] = useState(true);
  const [showPlayPauseBtn, setShowPlayPauseBtn] = useState(false);
  const [showDefaultVideoImage, setShowDefaultVideoImage] = useState(true);
  const [videoEnded, setVideoEnded] = useState(false);
  const [playViewCount, setPlayViewCount] = useState(0);
  const [shareCount, setShareCount] = useState(0);
  const [showPlayViewIcon, setShowPlayViewIcon] = useState(false);
  const [submitPostViewsExecute, setSubmitPostViewsExecute] = useState(false);

  const [shareBody1, setShareBody1] = useState('');
  const [shareBody2, setShareBody2] = useState('');
  const updateUserLocationRef = useRef(null);
  const [selectedReactions, setSelectedReactions] = useState([]);
  const [userReactions, setUserReactions] = useState({});
  const [isLikeServiceExecute, setIsLikeServiceExecute] = useState(false);

  // Hardcoded emotions for reactions (happy, angry, hmm, romantic)
  const hardcodedEmotions = HARDCODED_EMOTIONS.filter(emotion =>
    ['HAPPY', 'ANGRY', 'HMM', 'LOVED'].includes(emotion.value),
  );
  const [selectedEmotions, setSelectedEmotions] = useState([]);
  const [emotionSounds, setEmotionSounds] = useState({});
  const emotionRefs = useRef({});
  const emotionScaleAnims = useRef({});
  const [isFollowing, setIsFollowing] = useState(false);
  const [likeRemoveType, setLikeRemoveType] = useState({
    type: '',
    count: 0,
  });
  const [playlistLogo, setPlaylistLogo] = useState(null);
  const [playlistSequence, setPlaylistSequence] = useState('');
  const [isLandscape, setIsLandscape] = useState(false);
  const [forceLandscapeLayout, setForceLandscapeLayout] = useState(false); // Control landscape layout for fullscreen reels

  // Add orientation detection
  useEffect(() => {
    const updateOrientation = () => {
      const {width, height} = RNDimensions.get('window');
      setIsLandscape(width > height);
    };

    // Initial check
    updateOrientation();

    // Listen for orientation changes
    const subscription = RNDimensions.addEventListener(
      'change',
      updateOrientation,
    );

    return () => subscription?.remove();
  }, []);
  useEffect(() => {
    // if (fullScreen) {
    //     if (rowIndex == currentVisibleIndex) {
    //         if (!!videoPlayerRef.current) {
    //             videoPlayerRef.current.seek(0);
    //         }
    //         setVideoLoading(true);
    //         setPlayVideo((prevState) => true);
    //     }
    //     else {
    //         setPlayVideo((prevState) => false);
    //     }
    //     console.log("Date", new Date())
    //     console.log("rowIndex", rowIndex)
    //     console.log("currentVisibleIndex", currentVisibleIndex)
    // }
  }, [rowIndex, currentVisibleIndex]);

  useEffect(() => {
    setcardType(postCardType);
  }, [postCardType]);

  // Load emotion sounds
  useEffect(() => {
    const sounds = {};
    hardcodedEmotions.forEach(emotion => {
      if (emotion.audioPath) {
        sounds[emotion.value] = new Sound(
          emotion.audioPath,
          Sound.MAIN_BUNDLE,
          error => {
            if (error) {
              console.log('Failed to load sound for', emotion.value, error);
            }
          },
        );
      }
    });
    setEmotionSounds(sounds);

    // Cleanup sounds on unmount
    return () => {
      Object.values(sounds).forEach(sound => {
        if (sound) {
          sound.release();
        }
      });
    };
  }, []);

  // Function to get or create scale animation for an emotion
  const getEmotionScaleAnimation = emotionKey => {
    if (!emotionScaleAnims.current[emotionKey]) {
      emotionScaleAnims.current[emotionKey] = new Animated.Value(1);
    }
    return emotionScaleAnims.current[emotionKey];
  };

  // Function to perform scale animation for emotions
  const performScaleAnimation = (emotionKey, isSelected) => {
    // Don't animate if the emotion is already selected
    if (isSelected) return;

    const scaleAnim = getEmotionScaleAnimation(emotionKey);

    Animated.sequence([
      Animated.timing(scaleAnim, {
        toValue: 2,
        duration: 500,
        useNativeDriver: true,
      }),
      Animated.delay(500),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 500,
        useNativeDriver: true,
      }),
    ]).start();
  };

  useEffect(() => {
    // Check if itemData is not null or undefined
    if (!itemData) return;

    // Destructure itemData for easier access
    const {
      is_tagged = '',
      post_caption_tags = [],
      views = 0,
      shares = 0,
      media_type = '',
      reactions = [],
      user_reactions = {},
      is_following = '',
      sequence = '',
    } = itemData;

    // Set validUserHandleList
    setvalidUserHandleList(post_caption_tags);

    // Set playViewCount and shareCount
    setPlayViewCount(views);
    setShareCount(shares);

    // Set showTagUser
    const sTag = is_tagged === 'YES';
    setshowTagUser(sTag);

    // Set showPlayViewIcon
    setShowPlayViewIcon(media_type === 'VIDEO');

    // Share post link after 500ms if fullScreen and showSharePopup
    if (fullScreen && showSharePopup) {
      setTimeout(() => sharePostLink('UNLOCK_POST'), 500);
    }

    // Set selectedReactions
    setSelectedReactions(reactions);

    // Set userReactions
    setUserReactions(user_reactions);

    // Set isFollowing
    const followValue = is_following === 'YES';
    setIsFollowing(followValue);

    // Set playlistLogo and playlistSequence
    if (sequence && TempData.episodeData.url) {
      setPlaylistLogo(TempData.episodeData.url);
    }
    setPlaylistSequence(sequence);
  }, [
    itemData.profile_seq,
    itemData.post_seq,
    fullScreen,
    itemData.is_following,
  ]);

  useEffect(() => {
    if (__commentObj != undefined) {
      if (__commentObj.postSeq == postSeq) {
        let sCmnt = false;
        if (__commentObj.commentRight == 'YES') {
          sCmnt = true;
        }
        setshowCommentActive(sCmnt);
      }
    }
  }, [__commentObj]);

  useEffect(() => {
    let showBMWB = false;
    if (showBookmarkWarrings) {
      if (expiryPostDate != '9999-12-31') {
        showBMWB = true;
      }
    }
    setshowBMWarrings(showBMWB);
  }, [expiryPostDate]);

  useEffect(() => {
    if (!itemData) return;

    const {
      post_comments,
      is_liked,
      likes,
      is_bookmarked,
      is_commented,
      is_subscribed,
      profile_seq,
      post_type,
      profile_type,
      is_profile_subscribed,
      viewer_fee_display,
      viewer_fee,
      profile_fee_display,
      profile_fee,
      enable_comment,
      is_restricted,
      comments,
    } = itemData;

    setShowDefaultVideoImage(true);

    const decodePostCmt = decodeHtmlEntitessData(post_comments);
    setpostComments(decodePostCmt);

    const isLike = is_liked === 'YES';
    setlikeCount(likes);
    setPostIsLike(isLike);

    const is_bookmarkedV = is_bookmarked === 'YES';
    setpostIsBookmark(forceBookmark ? true : is_bookmarkedV);

    const comntShow = is_commented === 'YES';
    setshowCommentActive(comntShow);

    const isPostSubscribed = is_subscribed === 'YES';
    setpostIsSubs(isPostSubscribed);

    let postIsUnlock = false;
    let isProfilePaid = false;
    let isLikeBtnDisable = true;
    let isBookmarkDisable = true;
    let isCommentDisable = true;

    if (profile_seq === __profile_seq) {
      setshowBookmarkIcon(false);
    }

    if (isMyProfile) {
      postIsUnlock = true;
      isLikeBtnDisable = true;
      isBookmarkDisable = true;
      isCommentDisable = false;
      setshowBookmarkIcon(false);

      if (itemData.status === 'BLOCKED') {
        setblockMsgDB(itemData.block_reason);
        setblockPost(true);
      }
    } else if (isForceUnlock) {
      postIsUnlock = true;
      isLikeBtnDisable = false;
      isBookmarkDisable = false;
      isCommentDisable = false;
    } else {
      if (post_type === 'PAID') {
        if (isPostSubscribed) {
          postIsUnlock = true;
          isLikeBtnDisable = false;
          isBookmarkDisable = false;
          isCommentDisable = false;
        } else {
          postIsUnlock = false;
          isLikeBtnDisable = true;
          isBookmarkDisable = true;
          isCommentDisable = true;
        }
      } else if (profile_type === 'PAID') {
        isProfilePaid = true;
        if (is_profile_subscribed === 'YES') {
          postIsUnlock = true;
          isLikeBtnDisable = false;
          isBookmarkDisable = false;
          isCommentDisable = false;
        } else {
          postIsUnlock = false;
          isLikeBtnDisable = true;
          isBookmarkDisable = true;
          isCommentDisable = true;
        }
      } else {
        postIsUnlock = true;
        isLikeBtnDisable = false;
        isBookmarkDisable = false;
        isCommentDisable = false;
      }
    }

    if (isProfilePaid) {
      setunlockFees(profile_fee_display);
      setunlockFeesValue(profile_fee);
      setunlockMsg(ProfileUnlockMsg);
      setperMonthTxt(' PER MONTH');
      setisPaidProfile(true);
    } else {
      const valV = viewer_fee_display || viewer_fee;
      setunlockFees(valV);
      setunlockFeesValue(viewer_fee);
      setunlockMsg(PostUnlockMsg);
      setperMonthTxt('');
      setisPaidProfile(false);
    }

    setunlockPost(postIsUnlock);
    setlikeBtnDisable(isLikeBtnDisable);
    setbookmarkBtnDisable(isBookmarkDisable);

    if (enable_comment === 'YES') {
      if (is_restricted === 'YES') {
        setcommentBtnDisable(true);
      } else {
        setcommentBtnDisable(isCommentDisable);
      }
    } else {
      setcommentBtnDisable(true);
    }

    setCommentCount(comments);
  }, [itemData.post_seq, itemData.likes, itemData.is_liked, itemData.comments]);
  // useEffect(() => {
  //     if (!fullScreen && !showVideoContent) {
  //         if (appData.commentPostSeq == itemData.post_seq) {
  //             if (showCommentCountV) {
  //                 setShowCommentCount(true);
  //                 setshowCommentActive(true);
  //                 setCommentCount(appData.commentPostCount);
  //                 changeShowCommentCount(false);
  //                 appData.commentPostSeq = "";
  //                 appData.commentPostCount = "";
  //             }

  //         }
  //         const showCommentCountTimeOut = setTimeout(() => {
  //             setShowCommentCount(false);
  //         }, 4000);
  //         return () => {
  //             clearTimeout(showCommentCountTimeOut)
  //         }
  //     }
  // }, [appData.commentPostSeq, appData.commentPostCount])

  const unlockSheetRef = useRef(null);
  const threeDotMenuSheetRef = useRef(null);
  const openCommentSheetRef = useRef(null);
  let likeTimeout = null;
  const likeIconBtnClick = (type, count, isLike) => {
    // if (preventDoubleClick(appData.buttonClickTime,0.1)) {
    //     appData.buttonClickTime = new Date();
    if (isMyProfile) {
      setIsServiceExecute(false);
      setPostIsLike(false);
      Alert.alert('Error', 'Self Like is not Possible!');
      return;
    }
    let callRemoveService = false;
    if (type == REACTION_LIKE) {
      if (postIsLike) {
        setlikeCount(prevState => (prevState == 1 ? 0 : prevState - 1));
        callRemoveService = true;
      } else {
        setlikeCount(prevState => prevState + 1);
      }
    } else {
      setLikeRemoveType({
        type: type,
        count: count,
      });
      callRemoveService = isLike ? true : false;
      const dataObj = JSON.parse(JSON.stringify(userReactions));
      if (dataObj.hasOwnProperty(type)) {
        dataObj[type].selected = isLike ? 'NO' : 'YES';
        dataObj[type].count = isLike
          ? dataObj[type].count - 1
          : dataObj[type].count + 1;
      } else {
        dataObj[type] = {
          selected: isLike ? 'NO' : 'YES',
          count: isLike ? count - 1 : count + 1,
        };
      }
      setUserReactions(dataObj);
    }
    if (callRemoveService) {
      setIsServiceExecute(true);
      removePostLike(type);
    } else {
      setIsServiceExecute(true);
      submitPostLike(type);
    }
    // }
  };

  function submitPostLike(type) {
    if (type == REACTION_LIKE) {
      setIsLikeServiceExecute(true);
      setPostIsLike(current => true);
    }
    let hashMap = {
      _action_code: '11:SUBMIT_POST_LIKE',
      post_seq: postSeq,
      type: type,
    };
    let connector = new ServerConnector();
    connector.postData(
      hashMap,
      data => {
        // success method
        // setlikeCount(data.data.like_count);
        setIsLikeServiceExecute(false);
        sharePostLink('LIKE');
        if (type == 'LIKE') {
          // itemData.is_liked = "YES";
          // itemData.likes = parseInt(data.data.like_count);
        } else {
          const reaction = itemData.user_reactions[type];
          if (reaction) {
            // reaction.count = data.data.like_count;
            reaction.selected = 'YES';
          } else {
            itemData.user_reactions[type] = {
              // count: data.data.like_count,
              selected: 'YES',
            };
          }
        }

        if (type !== REACTION_LIKE) {
          clearPostCountDisplay(type);
        }
        homepagePostDataBackup.postStatusChangeData.push({
          post_seq: postSeq,
          type: 'LIKE',
          action_code: 'SUBMIT_POST_LIKE',
          reactionType: type,
          count: data.data.like_count,
          profileSeq: itemData.profile_seq,
        });
        if (fullScreen || showVideoContent) {
          appData._homePagePostRefresh = 'YES';
        }
      },
      (errorCode, errorMessage, data) => {
        // failure method
        if (_RedirectionErrorList.includes(errorCode)) {
          RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
        } else {
          if (errorCode == 'UE016') {
          }
          if (type !== REACTION_LIKE) {
            clearPostCountDisplay(type);
          }
          setIsLikeServiceExecute(false);
        }
      },
    );
  }
  useEffect(() => {
    const likeTimeoutV = setTimeout(() => {
      setLikeRemoveType({
        type: '',
        count: '',
      });
    }, 3000);
    return () => {
      clearTimeout(likeTimeoutV);
    };
  }, [likeRemoveType.type, likeRemoveType.count]);

  function clearPostCountDisplay(type) {
    if (likeTimeout != null) {
    }
  }
  function removePostLike(type) {
    if (type == REACTION_LIKE) {
      setIsLikeServiceExecute(true);
      setPostIsLike(current => false);
    }
    let hashMap = {
      _action_code: '11:REMOVE_POST_LIKE',
      post_seq: postSeq,
      type: type,
    };
    let connector = new ServerConnector();
    connector.postData(
      hashMap,
      data => {
        // success method
        setIsLikeServiceExecute(false);
        if (type == 'LIKE') {
          // itemData.is_liked = "NO";
          // itemData.likes = parseInt(data.data.like_count);
        } else {
          const reaction = itemData.user_reactions[type];
          if (reaction) {
            reaction.count = data.data.like_count;
            reaction.selected = 'NO';
          } else {
            itemData.user_reactions[type] = {
              count: data.data.like_count,
              selected: 'NO',
            };
          }
        }
        // setPostIsLike(current => !current);
        // setlikeCount(data.data.like_count);
        if (type !== REACTION_LIKE) {
          clearPostCountDisplay(type);
        }
        homepagePostDataBackup.postStatusChangeData.push({
          post_seq: postSeq,
          type: 'LIKE',
          action_code: 'REMOVE_POST_LIKE',
          reactionType: type,
          count: data.data.like_count,
          profileSeq: itemData.profile_seq,
        });
        if (fullScreen || showVideoContent) {
          appData._homePagePostRefresh = 'YES';
        }
      },
      (errorCode, errorMessage, data) => {
        // failure method
        if (_RedirectionErrorList.includes(errorCode)) {
          RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
        } else {
          if (type !== REACTION_LIKE) {
            clearPostCountDisplay(type);
          }
          setIsLikeServiceExecute(false);
        }
      },
    );
  }

  const bookmarkIconBtnClick = () => {
    if (preventDoubleClick(appData.buttonClickTime)) {
      appData.buttonClickTime = new Date();
      if (postIsBookmark) {
        // setIsServiceExecute(true);
        removePostBookmark();
      } else {
        if (expiryPostDate != '9999-12-31') {
          setSnackbarMsg(ErrorMessages.bookmarkPostExpiryHint);
          setsnackBarType('SUCCESS');
          setdisplaySnackbar(true);
          setrefreshSnackBar(Math.random());
        }
        submitPostBookmark();
      }
    }
  };
  function submitPostBookmark() {
    let hashMap = {
      _action_code: '11:ADD_BOOKMARK',
      post_seq: postSeq,
    };
    let connector = new ServerConnector();
    connector.postData(
      hashMap,
      data => {
        // success method
        setpostIsBookmark(true);
        sharePostLink('BOOKMARK');
        itemData.is_bookmarked = 'YES';
        if (fullScreen || showVideoContent) {
          appData._homePagePostRefresh = 'YES';
        }
        homepagePostDataBackup.postStatusChangeData.push({
          post_seq: postSeq,
          type: 'BOOKMARK',
          action_code: 'ADD_BOOKMARK',
          reactionType: '',
          count: 0,
          profileSeq: itemData.profile_seq,
        });
      },
      (errorCode, errorMessage, data) => {
        // failure method
        if (_RedirectionErrorList.includes(errorCode)) {
          RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
        } else {
        }
      },
    );
  }
  function removePostBookmark() {
    let hashMap = {
      _action_code: '11:REMOVE_BOOKMARK',
      post_seq: postSeq,
    };
    let connector = new ServerConnector();
    connector.postData(
      hashMap,
      data => {
        // success method
        setpostIsBookmark(false);
        itemData.is_bookmarked = 'NO';
        postCardClick('REMOVE_BOOKMARK', {});
        homepagePostDataBackup.postStatusChangeData.push({
          post_seq: postSeq,
          type: 'BOOKMARK',
          action_code: 'REMOVE_BOOKMARK',
          reactionType: '',
          count: 0,
          profileSeq: itemData.profile_seq,
        });
        if (fullScreen || showVideoContent) {
          appData._homePagePostRefresh = 'YES';
        }
      },
      (errorCode, errorMessage, data) => {
        // failure method
        if (_RedirectionErrorList.includes(errorCode)) {
          RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
        } else {
        }
      },
    );
  }
  const threeDotMenuClick = () => {
    //threeDotMenuSheetRef.current?.setModalVisible();
    setPlayVideo(prevState => false);
    setShowPlayPauseBtn(prevState => true);
    Keyboard.dismiss();
    setTimeout(() => {
      threeDotMenuSheetRef.current?.show();
    }, 100);

    // threeDotMenuSheetRef.current?.show();
  };
  const unlockBtnClick = () => {
    //unlockSheetRef.current?.setModalVisible();
    // navigation.navigate('SinglePostScreen', {
    //     postSeq: postSeq,
    // });
    // return;
    let __has_state_city_val = fullUserDetails.hasOwnProperty('_has_state_city')
      ? fullUserDetails._has_state_city
      : 'NO';
    if (__has_state_city_val == 'YES') {
      unlockSheetRef.current?.show();
      postCardClick('OPEN_UNLOCK_POPUP', {});
    } else {
      updateUserLocationRef.current?.show();
      postCardClick('OPEN_UNLOCK_POPUP', {});
      // setconfirmMsg(ErrorMessages.accNotFoundForPayMsg);
      // setshowConfirmPopup(true);
      // setshowConfirmPopupKey(Math.random());
      // setwarringsData({ clickType: "GO_ACCOUNT" });
    }
  };
  const confirmPopupPress = (clickId, obj) => {
    if (clickId == 'positive') {
      if (obj.clickType == 'GO_ACCOUNT') {
        navigation.navigate('AccountInfoScreen', {
          locationMandatory: 'YES',
        });
      }
    }
  };

  const unlockPostActionClick = (clickId, obj) => {
    if (clickId == 'negetive') {
      unlockSheetRef.current?.hide();
      postCardClick('CLOSE_UNLOCK_POPUP', {});
    }
    if (clickId == 'close') {
      unlockSheetRef.current?.hide();
      postCardClick('CLOSE_UNLOCK_POPUP', {});
      appData.__HomePageRefresh = Math.random();
      appData._profilePageRefresh = true;
      if (isPaidProfile) {
        setTimeout(() => {
          navigation.replace('OthersProfileScreen', {
            profileSeq: itemData.profile_seq,
          });
        }, 1000);
      } else {
        unlockSheetRef.current?.hide();
        postCardClick('CLOSE_UNLOCK_POPUP', {});
        appData.__HomePageRefresh = Math.random();
        appData._profilePostPageRefresh = true;
        setTimeout(() => {
          navigation.replace('UnlockSinglePostScreen', {
            postSeq: itemData.post_seq,
            postProfileSeq: itemData.profile_seq,
          });
        }, 1000);
      }
    }
  };
  const ThreeDotMenuPress = (clickId, obj) => {
    if (clickId == 'blockPost') {
      setblockMsg(obj.msg);
      setblockPost(true);
      setIsServiceExecute(true);
      threeDotMenuSheetRef.current?.hide();
    } else if (clickId == 'deletePost') {
      threeDotMenuSheetRef.current?.hide();
      postCardClick('DELETE_POST', {});
    } else if (clickId == 'sharePost') {
      shareBtnPress();
    } else if (clickId == 'editPost') {
      threeDotMenuSheetRef.current?.hide();
    }
  };
  const showPostImage = mediaUri => {
    navigation.navigate('ImageDisplayScreen', {
      mediaUri: mediaUri,
    });
  };
  const __ProfileSeq = fullUserDetails.hasOwnProperty('_profile_seq')
    ? fullUserDetails._profile_seq
    : -1;
  const goToProfile = profileSeq => {
    if (fullScreen && showVideoContent) {
      setPlayVideo(prevState => false);
      setShowPlayPauseBtn(prevState => true);
    }
    if (__ProfileSeq == profileSeq) {
      if (!isMyProfile) {
        navigation.navigate('HomeScreen', {screen: 'ProfileFeed'});
      }
    } else {
      if (cardType == 'TAGGED') {
        navigation.push('OthersProfileScreen', {
          profileSeq: profileSeq,
        });
      } else {
        navigation.navigate('OthersProfileScreen', {
          profileSeq: profileSeq,
        });
      }
    }
  };
  const videoBtnPress = (media_file, media_cover) => {
    navigation.navigate('VideoDisplayScreen', {
      mediaUri: media_file,
      thumbnailUri: media_cover,
    });
  };
  const [showPeopleList, setshowPeopleList] = useState(false);
  const postTagPeoplePress = () => {
    navigation.navigate('TagPeopleListScreen', {
      postSeq: itemData.post_seq,
    });
  };
  const postTagNamePress = userHandleId => {
    let cameHndleId = userHandleId;
    let userHandle = cameHndleId.substring(2);
    getUserSeqService(userHandle);
  };
  function getUserSeqService(userHandle) {
    let hashMap = {
      _action_code: '11:GET_USER_SEQ',
      user_handle: userHandle,
    };
    let connector = new ServerConnector();
    connector.postData(
      hashMap,
      data => {
        // success method
      },
      (errorCode, errorMessage, data) => {
        // failure method
        if (_RedirectionErrorList.includes(errorCode)) {
          RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
        } else {
          setSnackbarMsg(errorMessage);
          setsnackBarType('SUCCESS');
          setdisplaySnackbar(true);
          setrefreshSnackBar(Math.random());
        }
      },
    );
  }
  const rStyle = useAnimatedStyle(() => ({
    transform: [{scale: Math.max(scale.value, 0)}],
  }));
  const onDoubleTap = useCallback(() => {
    if (!likeBtnDisable) {
      scale.value = withSpring(1, undefined, isFinished => {
        if (isFinished) {
          scale.value = withDelay(500, withSpring(0));
        }
      });
      likeThePost(REACTION_LIKE);
    }
  }, [likeBtnDisable, postIsLike]);
  const onSingleTap = useCallback(() => {
    if (!fullScreen) {
      // showImage();
      openSinglePostScreen();
    }

    // opacity.value = withTiming(0, undefined, (isFinished) => {
    //     if (isFinished) {
    //         runOnJS(showImage)();
    //     }
    // });
  }, [postIsLike, likeCount, postIsBookmark, commentCount]);
  const showImage = () => {
    showPostImage(itemData.media_file);
  };
  const likeThePost = type => {
    if (!postIsLike) {
      likeIconBtnClick(type, likeCount, postIsLike);
    }
  };

  // Emotion handler function similar to OptionSelectionItem
  const onEmotionPress = emotion => {
    const isSelected = selectedEmotions.includes(emotion.value);

    if (!isSelected) {
      // Play Lottie animation if available (try both fullscreen and homescreen refs)
      if (emotion.lottiePath) {
        const fullscreenRef = emotionRefs.current[emotion.value];
        const homescreenRef = emotionRefs.current[`home_${emotion.value}`];

        if (fullscreenRef) {
          fullscreenRef.reset();
          fullscreenRef.play();
        }
        if (homescreenRef) {
          homescreenRef.reset();
          homescreenRef.play();
        }
      }

      // Perform scale animation for inactive emotions only
      if (emotion.lottiePath || emotion.icon) {
        performScaleAnimation(emotion.value, isSelected);
      }

      // Play sound if available
      if (emotionSounds[emotion.value]) {
        emotionSounds[emotion.value].play(success => {
          if (success) {
            console.log('Successfully played sound for', emotion.value);
          } else {
            console.log('Failed to play sound for', emotion.value);
          }
        });
      }

      // Trigger vibration
      if (emotion.vibrationPattern) {
        Vibration.vibrate(emotion.vibrationPattern);
      } else {
        Vibration.vibrate(100);
      }

      // Add to selected emotions
      setSelectedEmotions([...selectedEmotions, emotion.value]);
    } else {
      // Remove from selected emotions
      setSelectedEmotions(
        selectedEmotions.filter(item => item !== emotion.value),
      );
    }
  };
  const rStyleVideo = useAnimatedStyle(() => ({
    transform: [{scale: Math.max(scaleVideo.value, 0)}],
  }));
  const onDoubleTapVideo = useCallback(() => {
    if (!likeBtnDisable) {
      scaleVideo.value = withSpring(1, undefined, isFinished => {
        if (isFinished) {
          scaleVideo.value = withDelay(500, withSpring(0));
        }
      });
      likeThePost(REACTION_LIKE);
    }
  }, [likeBtnDisable, postIsLike]);
  const onSingleTapVideo = useCallback(() => {
    // if (fullScreen) {
    //     if (!playVideo) {
    //         if (videoEnded) {
    //             videoPlayerRef.current.seek(0);
    //             setVideoEnded(false);
    //         }

    //     }
    //     setPlayVideo((prevState) => !prevState)
    //     setShowPlayPauseBtn((prevState) => !prevState);
    // }
    // else {
    //     openSinglePostScreen();
    //     // showVideo();
    // }
    if (fullScreen) {
      if (!playVideo) {
        if (videoEnded) {
          videoPlayerRef.current.seek(0);
          setVideoEnded(false);
        }
      }
      setPlayVideo(prevState => !prevState);
      setShowPlayPauseBtn(prevState => !prevState);
    } else {
      navigation.navigate('VideoContentScreen', {
        postSeq: itemData.post_seq,
        postProfileSeq: itemData.profile_seq,
        cameFrom: 'POST',
      });
    }
  }, [playVideo, postIsLike, likeCount, postIsBookmark, commentCount]);
  const showVideo = () => {
    videoBtnPress(itemData.media_file, itemData.media_cover);
  };
  const ImageOverlay = () => {
    return (
      <LinearGradient
        colors={['#000000', 'transparent', 'transparent', '#000000']}
        locations={[0, 0.11, 0.8, 0.97]}
        style={style.NSLinearGradient}
      />
    );
  };
  const ImageOverlayNew = () => {
    return (
      <LinearGradient
        colors={['#000000', 'transparent', 'transparent', '#000000']}
        locations={[0, 0.18, 0.8, 0.97]}
        style={style.NSSLinearGradient}
      />
    );
  };
  const VideoOverlay = () => {
    return (
      <LinearGradient
        colors={['#000000', 'transparent', 'transparent', '#000000']}
        locations={[0, 0.11, 0.8, 0.97]}
        style={style.videoOverlayGrad}
      />
    );
  };
  const [videoDisplayCount, setVideoDisplayCount] = useState(0);
  const onVideoEnd = () => {
    setVideoDisplayCount(prevState => prevState + 1);
    if (!repeatVideo) {
      setVideoEnded(true);
      setPlayVideo(prevState => false);
      setShowPlayPauseBtn(true);
    }
    if (videoDisplayCount > 3) {
      // setVideoCompletedPercentage(0)
      setVideoEnded(true);
      setPlayVideo(prevState => false);
      setShowPlayPauseBtn(true);
    }
  };
  const snapPoints = useMemo(() => ['40%'], []);
  const [openCommentPopup, setOpenCommentPopup] = useState(false);
  const [openCommentPopupKey, setOpenCommentPopupKey] = useState(Math.random());
  const commentBtnPress = (singlePost = true) => {
    setPlayVideo(prevState => false);
    navigation.navigate('CommentScreen', {
      postSeq: itemData.post_seq,
      postProfileSeq: itemData.profile_seq,
    });
    // if (singlePost) {
    //     setOpenCommentPopup(true);
    //     setOpenCommentPopupKey(Math.random());
    // }
    // else {
    //     navigation.navigate('CommentScreen', {
    //         postSeq: itemData.post_seq, postProfileSeq: itemData.profile_seq,
    //     });
    // }

    // openCommentSheetRef.current?.present();
    // navigation.navigate('CommentScreen', {
    //     postSeq: itemData.post_seq, postProfileSeq: itemData.profile_seq,
    // });
  };
  const commentComponentClick = (clickID, obj) => {
    if (clickID == 'SUBMIT') {
      setIsServiceExecute(true);
      setCommentCount(obj.comment_count);
    } else {
      setOpenCommentPopup(false);
      setOpenCommentPopupKey(Math.random());
    }
  };
  const openSinglePostScreen = () => {
    let updatedObj = {
      ...itemData,
      likes: likeCount,
      is_liked: postIsLike ? 'YES' : 'NO',
      is_bookmarked: postIsBookmark ? 'YES' : 'NO',
      comments: commentCount,
      is_commented: showCommentActive ? 'YES' : 'NO',
    };
    changeSingleProfileObj(updatedObj);
    navigation.navigate('SinglePostScreen', {
      postSeq: postSeq,
      isServiceExecute: 'YES',
    });
  };

  const onVideoLoaded = () => {
    setVideoLoading(false);
  };

  const videoLoadStart = () => {
    // console.log("Video Start")
    setShowDefaultVideoImage(false);
  };
  function closeActionSheet() {}
  function onVidePlayStart() {
    let currentTime = new Date();
    let timeDiff = Math.abs(appData.videoViewExecuteTime - currentTime);
    let second = Math.floor(timeDiff / 1000);
    if (second >= 3) {
      appData.videoViewExecuteTime = new Date();
      if (__ProfileSeq != itemData.profile_seq) {
        if (!submitPostViewsExecute) {
          setSubmitPostViewsExecute(true);
          submitPostViewService();
        }
      }
    }
  }
  function submitPostViewService() {
    let hashMap = {
      _action_code: '11:SUBMIT_POST_VIEW',
      post_seq: postSeq,
      profile_seq: itemData.profile_seq,
    };
    let connector = new ServerConnector();
    connector.postData(
      hashMap,
      data => {
        // success method
        setPlayViewCount(data.data.view_count);
      },
      (errorCode, errorMessage, data) => {
        // failure method
      },
    );
  }
  const reelsVideoStart = () => {
    let currentTime = new Date();
    let timeDiff = Math.abs(appData.videoViewExecuteTime - currentTime);
    let second = Math.floor(timeDiff / 1000);
    if (second >= 3) {
      appData.videoViewExecuteTime = new Date();
      if (__ProfileSeq != itemData.profile_seq) {
        if (!submitPostViewsExecute) {
          setSubmitPostViewsExecute(true);
          submitPostViewService();
        }
      }
    }
  };
  const shareBtnPress = () => {
    submitShareCountService();
    sharePostLink('SHARE');
  };
  const sharePostLink = async type => {
    let body1 = '';
    let body2 = '';
    if (type == 'SHARE') {
      let copyLinkText = creationOfCopyLink('POST', itemData.post_seq);
      const shareOptions = {
        message: 'Exclusive content on SoTrue\n',
        url: copyLinkText,
      };
      try {
        const shareResponse = await Share.open(shareOptions);
      } catch (error) {
        // console.log(error.message);
      }
      return;
    } else {
      if (type == 'BOOKMARK') {
        setShareBody1(ErrorMessages.bookmarkPostBody1);
        setShareBody2(ErrorMessages.bookmarkPostBody2);
        openShareModal();
      } else if (type == 'LIKE') {
        _getFirstTimeLikePost(data => {
          if (data != null) {
            if (data == 'YES') {
              _setFirstTimeLikePost('NO');
              setShareBody1(ErrorMessages.likePostBody1);
              setShareBody2(ErrorMessages.likePostBody2);
              openShareModal();
            }
          } else {
            _setFirstTimeLikePost('NO');
            setShareBody1(ErrorMessages.likePostBody1);
            setShareBody2(ErrorMessages.likePostBody2);
            openShareModal();
          }
        });
      } else if (type == 'UNLOCK_POST') {
        _getFirstTimeUnlockPost(data => {
          if (data != null) {
            if (data == 'YES') {
              _setFirstTimeUnlockPost('NO');
              setShareBody1(ErrorMessages.unlockPostBody1);
              setShareBody2(ErrorMessages.unlockPostBody2);
              openShareModal();
            }
          } else {
            _setFirstTimeUnlockPost('NO');
            setShareBody1(ErrorMessages.unlockPostBody1);
            setShareBody2(ErrorMessages.unlockPostBody2);
            openShareModal();
          }
        });
      }
    }
  };
  function submitShareCountService() {
    let hashMap = {
      _action_code: '11:UPDATE_SHARE_COUNT',
      post_seq: postSeq,
    };
    let connector = new ServerConnector();
    connector.postData(
      hashMap,
      data => {
        // success method
      },
      (errorCode, errorMessage, data) => {
        // failure method
      },
    );
  }
  const [displaySharePopup, setDisplaySharePopup] = useState(false);
  const openShareModal = () => {
    setDisplaySharePopup(true);
  };
  const sharePostProfileCallback = (clickID, obj) => {
    if (clickID == 'CLOSE') {
      setDisplaySharePopup(false);
    }
  };
  const updateUserLocationPopupPress = (clickId, obj) => {
    if (clickId == 'negetive') {
      updateUserLocationRef.current?.hide();
    }
    if (clickId == 'close') {
      updateUserLocationRef.current?.hide();
      unlockSheetRef.current?.show();
      postCardClick('OPEN_UNLOCK_POPUP', {});
    }
  };
  const [showLikeCountBox, setShowLikeCountBox] = useState(false);
  useEffect(() => {
    if (isLikeServiceExecute) {
      setShowLikeCountBox(true);
    }
    const showCountTimeOut = setTimeout(() => {
      setShowLikeCountBox(false);
    }, 3000);
    return () => {
      clearTimeout(showCountTimeOut);
    };
  }, [postIsLike]);
  const [videoCompletedPercentage, setVideoCompletedPercentage] = useState(0);
  const onVideoProgress = useCallback(e => {
    const currentTime = parseFloat(e.currentTime);
    const playableDuration = parseFloat(e.playableDuration);
    const percentage = (currentTime / playableDuration) * 100;
    setVideoCompletedPercentage(percentage);
  }, []);
  const VideoOverlayReels = () => {
    return (
      <LinearGradient
        colors={['#00000070', 'transparent', 'transparent', '#000000']}
        locations={[0, 0.2, 0.8, 0.97]}
        style={style.videoOverlayGrad}
      />
    );
  };
  const [followServiceLoading, setFollowServiceLoading] = useState(false);
  const followBtnPress = () => {
    setFollowServiceLoading(true);
    if (isFollowing) {
      unFollowProfile();
    } else {
      followProfile();
    }
  };
  function followProfile() {
    let hashMap = {
      _action_code: '11:FOLLOW_PROFILE',
      follow_profile_seq: itemData.profile_seq,
    };
    let connector = new ServerConnector();
    connector.postData(
      hashMap,
      data => {
        // success method
        setFollowServiceLoading(false);
        setIsFollowing(true);
        homepagePostDataBackup.postStatusChangeData.push({
          post_seq: postSeq,
          type: 'FOLLOW',
          action_code: 'FOLLOW_PROFILE',
          reactionType: '',
          count: 0,
          profileSeq: itemData.profile_seq,
        });
        if (fullScreen || showVideoContent) {
          appData._homePagePostRefresh = 'YES';
        }
      },
      (errorCode, errorMessage, data) => {
        // failure method
        if (_RedirectionErrorList.includes(errorCode)) {
          RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
        } else {
          setFollowServiceLoading(false);
          setIsFollowing(false);
        }
      },
    );
  }
  function unFollowProfile() {
    let hashMap = {
      _action_code: '11:UNFOLLOW_PROFILE',
      un_profile_seq: itemData.profile_seq,
    };
    let connector = new ServerConnector();
    connector.postData(
      hashMap,
      data => {
        // success method
        setIsFollowing(false);
        setFollowServiceLoading(false);
        homepagePostDataBackup.postStatusChangeData.push({
          post_seq: postSeq,
          type: 'FOLLOW',
          action_code: 'UNFOLLOW_PROFILE',
          reactionType: '',
          count: 0,
          profileSeq: itemData.profile_seq,
        });
        if (fullScreen || showVideoContent) {
          appData._homePagePostRefresh = 'YES';
        }
      },
      (errorCode, errorMessage, data) => {
        // failure method
        if (_RedirectionErrorList.includes(errorCode)) {
          RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
        } else {
          setIsFollowing(true);
          setFollowServiceLoading(false);
        }
      },
    );
  }

  // Determine if we should apply landscape layout for fullscreen reels
  const shouldApplyLandscapeLayout = showVideoContent && isLandscape;

  return (
    <View>
      <BottomSheetModalProvider>
        {showVideoContent ? (
          //{*********FULLSCREEN REEL *************}

          <View
            style={{
              height: AVL_HEIGHT,
              width: AVL_WIDTH,
              justifyContent: 'center', // Center vertically
              alignItems: 'center', // Center horizontally

              backgroundColor: '#000000', // Black background instead of red
              position: 'relative',
            }}>
            {/* <View style={{ ...style.videoResolutionContainer, zIndex: 1 }}>
                                <ResolutionVideoSwitch />
                            </View> */}
            {/* {console.log("Video Content Post Executed " + itemData.post_seq, new Date())} */}
            {/* {
                                unlockPost ? */}
            <View
              style={{
                ...style.playListCountContainer,
                zIndex: 1,
                // marginStart: 4,
              }}>
              <View style={style.NSProfileView}>
                <View>
                  {cameFrom == 'EPISODE' ? (
                    <View style={style.playListBox}>
                      <View style={style.playlistCircleBox}>
                        <Image
                          source={
                            playlistLogo != null
                              ? {uri: playlistLogo}
                              : PlaylistPlaceholder
                          }
                          style={style.playlistCover}
                        />
                      </View>
                      <EntutoTextView
                        style={{
                          ...style.playListCountText,
                          color: '#FFFFFF',
                        }}>
                        {playlistSequence}
                      </EntutoTextView>
                    </View>
                  ) : (
                    <>
                      {showPlayViewIcon ? (
                        <View
                          style={{
                            ...style.NSActionIconBox,
                            paddingStart: 0,
                            marginBottom: 8,
                            flexDirection: 'row',
                          }}>
                          <Image
                            style={{
                              ...style.NSActionIcon,
                              tintColor: '#FFFFFF',
                              height: 24,
                              width: 24,
                            }}
                            source={PlayViewCount}
                            resizeMode="contain"
                          />
                          <EntutoTextView
                            style={{
                              ...style.NSActionCountTxt,
                              color: '#FFFFFF',
                            }}>
                            {playViewCount}
                          </EntutoTextView>
                        </View>
                      ) : null}
                    </>
                  )}

                  {/* <EntutoTextView style={style.NSProfileID}>{UserHandlePrefix}{itemData.user_handle}</EntutoTextView> */}
                </View>
              </View>
            </View>
            {/* : null} */}
            {/* Back button with landscape layout support */}
            <View
              style={{
                position: 'absolute',
                top: shouldApplyLandscapeLayout ? 20 : 20,
                left: shouldApplyLandscapeLayout ? 20 : 20,
                zIndex: 100,
              }}>
              {/* <TouchableOpacity
                style={{paddingHorizontal: 10, paddingVertical: 10}}
                onPress={() => navigation.goBack()}>
                <Image
                  style={{
                    width: 24,
                    height: 24,
                    tintColor: '#FFFFFF',
                  }}
                  source={require('../../assets/Images/icon/Arrow.png')}
                  resizeMode="contain"
                />
              </TouchableOpacity> */}
            </View>

            {!blockPost ? (
              <View
                style={{
                  ...style.NSTopHeaderThreeDotIcon,
                  right: shouldApplyLandscapeLayout ? 20 : 8,
                  top: shouldApplyLandscapeLayout
                    ? 20
                    : style.NSTopHeaderThreeDotIcon.top,
                }}>
                <TouchableOpacity
                  style={{paddingHorizontal: 10, paddingBottom: 10}}
                  onPress={() => threeDotMenuClick()}>
                  <Image
                    style={style.headerOptionIcon}
                    source={ThreeDotVerticalIcon}
                  />
                </TouchableOpacity>
              </View>
            ) : null}

            {blockPost ? (
              <>
                {blockMsg.length != 0 ? (
                  <BottomSheetSuccessMsg
                    successMsg={blockMsg}
                    showCloseBtn={false}
                  />
                ) : null}
                {blockMsgDB.length != 0 ? (
                  <View style={style.blockErrMsgMainBox}>
                    <View style={style.blockErrMsgBox}>
                      <EntutoTextView style={style.blockErrMsgHeading}>
                        Post Blocked
                      </EntutoTextView>
                      <EntutoTextView style={style.blockErrMsg}>
                        {blockMsgDB}
                      </EntutoTextView>
                    </View>
                  </View>
                ) : null}
              </>
            ) : null}

            {unlockPost ? (
              <View style={{flex: 1, flexDirection: 'column', width: '100%'}}>
                {showDefaultVideoImage ? (
                  <View
                    style={{
                      backgroundColor: 'black',
                      height: '100%',
                      width: '100%',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                    <ActivityIndicator
                      size={24}
                      style={{
                        backgroundColor: '#00000080',
                        zIndex: 3,
                        padding: 20,
                        borderRadius: 15,
                      }}
                    />
                    <View
                      style={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        alignItems: 'center',
                        justifyContent: 'center',
                        zIndex: 1,
                      }}>
                      <ImageBackground
                        imageStyle={{borderRadius: 0}}
                        source={
                          hasImageUrlExist(itemData.media_cover)
                            ? {uri: itemData.media_cover}
                            : null
                        }
                        style={style.videPostImage}></ImageBackground>
                    </View>
                  </View>
                ) : null}
                <TapGestureHandler
                  waitFor={doubleTapVideoRef}
                  onActivated={onSingleTapVideo}>
                  <View style={style.videoBox}>
                    <TapGestureHandler
                      maxDelayMs={250}
                      ref={doubleTapVideoRef}
                      numberOfTaps={2}
                      onActivated={onDoubleTap}>
                      <Animated.View>
                        <FullScreenPlayer
                          insets={insets}
                          videoPlayerRef={videoPlayerRef}
                          media_cover={itemData.media_cover}
                          media_file={itemData.media_file}
                          playVideo={!playVideo}
                          onLoadStart={() => reelsVideoStart()}
                          onLoad={() => videoLoadStart()}
                          onBuffer={() => onVideoLoaded()}
                          height={AVL_HEIGHT}
                          style={{...style.NSPostVideo, height: AVL_HEIGHT}}
                          isLandscape={isLandscape}
                        />
                        {/* <Video
                                                            ref={videoPlayerRef}
                                                            poster={hasImageUrlExist(itemData.media_cover) ? itemData.media_cover : null}
                                                            posterResizeMode={'cover'}
                                                            source={{ uri: itemData.media_file }}
                                                            resizeMode='contain'
                                                            repeat={true}
                                                            paused={!playVideo}
                                                            muted={false}
                                                            onLoadStart={() => reelsVideoStart()}
                                                            // onProgress={(e) => onVideoProgress(e)}
                                                            onLoad={() => videoLoadStart()}
                                                            onBuffer={() => onVideoLoaded()}
                                                            onError={(e) => console.log(e)}
                                                            onEnd={() => onVideoEnd()}
                                                            style={{ ...style.NSPostVideo, height: AVL_HEIGHT }}
                                                        /> */}
                        <VideoOverlayReels />
                        {/* <AnimatedImage
                          source={HeartVideoActive}
                          style={[
                            style.videPostHeartImage,
                            {
                              shadowOffset: {
                                width: 0,
                                height: Platform.OS == 'ios' ? 2 : 20,
                              },
                              shadowOpacity: 0.35,
                              shadowRadius: Platform.OS == 'ios' ? 2 : 35,
                              position: 'absolute',
                              top: '40%',
                              left: '35%',
                              zIndex: 3,
                            },
                            rStyle,
                          ]}
                          resizeMode={'contain'}
                        /> */}
                        {videoLoading ? (
                          <View style={style.playBtnBox}>
                            <ActivityIndicator size={32} />
                          </View>
                        ) : null}
                        {!playVideo ? (
                          <View style={style.playBtnBox}>
                            <Image
                              style={style.playBtnBoxIcon}
                              source={PlayBtnIconFilled}
                              resizeMode="cover"
                            />
                          </View>
                        ) : null}
                      </Animated.View>
                    </TapGestureHandler>
                  </View>
                </TapGestureHandler>
                {/* <VideoOverlay /> */}
              </View>
            ) : (
              <View style={{...style.lockBox, height: AVL_HEIGHT}}>
                <ImageBackground
                  style={{...style.postFuzzayImage, height: AVL_HEIGHT}}
                  source={
                    hasImageUrlExist(itemData.fuzzy_image)
                      ? {uri: itemData.fuzzy_image}
                      : null
                  }>
                  {/* <LinearGradient colors={['#FFFFFFE5', '#FFFFFF33']}
                                                start={{ x: 0, y: 0 }} end={{ x: 1, y: 0 }}
                                                style={style.NSLinearGradient}
                                            /> */}
                  <VideoOverlayReels />
                  <View style={{...style.lockBtnBox, top: '35%'}}>
                    <Image
                      style={{
                        ...style.lockBoxIcon,
                        height: 64,
                        width: 52,
                        marginBottom: 20,
                      }}
                      source={LockIcon}
                      resizeMode="contain"
                    />
                    {isPaidProfile ? (
                      <TouchableOpacity
                        style={style.lockButton}
                        onPress={() => unlockBtnClick()}>
                        <View
                          style={{
                            ...style.lockBtnTextBox,
                            alignItems: 'center',
                          }}>
                          <View>
                            <EntutoTextView style={style.lockButtonTxt}>
                              {unlockMsg}
                            </EntutoTextView>
                          </View>
                          <View>
                            <EntutoTextView style={style.lockButtonPriceTxt}>
                              {CurrencySymbol}
                              {unlockFees}
                            </EntutoTextView>
                          </View>
                          <View>
                            <EntutoTextView style={style.lockButtonTxt}>
                              {perMonthTxt}
                            </EntutoTextView>
                          </View>
                        </View>
                      </TouchableOpacity>
                    ) : (
                      <TouchableOpacity
                        style={style.lockButton}
                        onPress={() => unlockBtnClick()}>
                        <View style={style.lockBtnTextBox}>
                          <View>
                            <EntutoTextView style={style.lockButtonTxt}>
                              {unlockMsg}
                            </EntutoTextView>
                          </View>
                          <View>
                            <EntutoTextView style={style.lockButtonPriceTxt}>
                              {CurrencySymbol}
                              {unlockFees}
                            </EntutoTextView>
                          </View>
                        </View>
                      </TouchableOpacity>
                    )}
                  </View>
                </ImageBackground>
              </View>
            )}

            {!blockPost ? ( // removed on 20_11_24 && (!unlockPost|| !playVideo)
              <>
                {/* Profile section moved to bottom left */}
                <View
                  style={{
                    ...style.likeBtnBox,
                    left: shouldApplyLandscapeLayout ? 20 : 0,
                    zIndex: 99,
                    bottom: shouldApplyLandscapeLayout
                      ? 20
                      : itemData.media_type == 'IMAGE'
                      ? 10
                      : BOTTOM_ACTION_BOX_GAP,
                    color: '#FFFFFF',
                    ...(shouldApplyLandscapeLayout && {
                      position: 'absolute',
                      width: '60%',
                      maxWidth: 400,
                    }),
                  }}>
                  {/*REELS HEADER Start*/}
                  <View style={style.reelProfileContainer}>
                    <ProgressiveImage
                      style={{
                        ...style.NSProfileViewImage,
                        color: '#FFFFFF',
                      }}
                      source={
                        hasImageUrlExist(itemData.profile_picture)
                          ? {uri: itemData.profile_picture}
                          : null
                      }
                      defaultImageSource={ProfileImagePlaceholder}
                      resizeMode="cover"
                    />
                    <View>
                      <View
                        style={{
                          flexDirection: 'row',
                          alignItems: 'center',
                          marginBottom: 6,
                        }}>
                        <TouchableOpacity
                          onPress={() => goToProfile(itemData.profile_seq)}
                          style={{
                            flexDirection: 'row',
                            alignItems: 'center',
                          }}>
                          <EntutoTextView
                            style={{
                              ...style.headerDisplayName,
                              color: '#FFFFFF',
                            }}>
                            {itemData.display_name}
                          </EntutoTextView>
                          {itemData.is_verified == 'YES' ? (
                            <Image
                              style={style.verifiedIcon}
                              source={VerifiedIcon}
                              resizeMode={'contain'}
                            />
                          ) : null}
                        </TouchableOpacity>
                        <TouchableOpacity
                          onPress={() => followBtnPress()}
                          disabled={followServiceLoading}>
                          <View
                            style={{
                              ...style.bellowFollowBtn,
                              borderColor: 'white',
                            }}>
                            {followServiceLoading ? (
                              <ActivityIndicator size={12} />
                            ) : (
                              <EntutoTextView
                                style={{
                                  ...style.bellowFollowBtnText,
                                  color: 'white',
                                }}>
                                {isFollowing ? 'FOLLOWING' : 'FOLLOW'}
                              </EntutoTextView>
                            )}
                          </View>
                        </TouchableOpacity>
                      </View>
                      <EntutoTextView style={style.taggedText}>
                        <Text style={{color: '#FFFFFF', fontWeight: 'bold'}}>
                          Cast:
                        </Text>
                        &nbsp;Siddhantkakar, prithvirajprasad ...
                      </EntutoTextView>
                    </View>
                  </View>
                  <View
                    style={{
                      ...style.bellowDescription,
                      width: '90%',
                      // remove color from here!
                    }}>
                    <DescriptionCaptionStyle
                      validHandleList={validUserHandleList}
                      mainText="Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris."
                      mainTextStyle={{
                        ...style.bellowCommentText,
                        color: '#FFFFFF', // this will correctly override the theme color
                      }}
                      highlightStyle={{
                        ...defaultStyle.boldTagTxt,
                        color: '#FFFFFF', // this will override the theme color on tagged handles
                      }}
                      numberOfLines={2}
                    />
                  </View>
                </View>
                {/*REELS HEADER END*/}
                {/* Reactions section on bottom right */}
                <View
                  style={{
                    ...style.likeBtnBox,
                    right: shouldApplyLandscapeLayout ? 20 : 0,
                    zIndex: 99,
                    alignItems: 'flex-end',
                    bottom: shouldApplyLandscapeLayout
                      ? '50%'
                      : itemData.media_type == 'IMAGE'
                      ? 10
                      : BOTTOM_ACTION_BOX_GAP,
                    ...(shouldApplyLandscapeLayout && {
                      position: 'absolute',
                      transform: [
                        {translateY: RNDimensions.get('window').height / 2},
                      ],
                      flexDirection: 'column',
                      alignItems: 'center',
                      width: 'auto',
                      height: 'auto',
                    }),
                  }}>
                  {/* Hardcoded emotions reactions */}
                  <View
                    style={{
                      marginBottom: isLandscape ? 5 : 20,
                    }}>
                    {hardcodedEmotions.map((emotion, i) => {
                      const isSelected = selectedEmotions.includes(
                        emotion.value,
                      );
                      return (
                        <View
                          key={i}
                          style={{
                            ...style.NSActionIconBox,
                            ...style.iconGap,
                            paddingStart: 16,
                            marginBottom: 0,
                            alignItems: 'center',
                          }}>
                          <TouchableOpacity
                            disabled={likeBtnDisable || !unlockPost}
                            onPress={() => onEmotionPress(emotion)}>
                            <Animated.View
                              style={{
                                transform: [
                                  {
                                    scale: getEmotionScaleAnimation(
                                      emotion.value,
                                    ),
                                  },
                                ],
                              }}>
                              {emotion.lottiePath ? (
                                <LottieView
                                  ref={ref => {
                                    if (ref) {
                                      emotionRefs.current[emotion.value] = ref;
                                    }
                                  }}
                                  source={emotion.lottiePath}
                                  style={{
                                    height: isLandscape ? 35 : 40,
                                    width: isLandscape ? 35 : 40,
                                  }}
                                  loop={false}
                                  autoPlay={false}
                                />
                              ) : (
                                <Image
                                  source={emotion.icon}
                                  style={[
                                    {
                                      height: isLandscape ? 30 : 40,
                                      width: isLandscape ? 30 : 40,
                                    },
                                    {
                                      tintColor: isSelected
                                        ? theme.colors.primaryColor
                                        : theme.colors.fullScreenIconTintColor,
                                    },
                                  ]}
                                  resizeMode="contain"
                                />
                              )}
                            </Animated.View>
                          </TouchableOpacity>
                          {/* Display reaction count below the icon */}
                          <EntutoTextView
                            style={{
                              color: '#FFFFFF',
                              fontSize: theme.calculateFontSizeNew(10),
                              textAlign: 'center',
                              marginTop: isLandscape ? 2 : 4,
                            }}>
                            {emotion.count || 0}
                          </EntutoTextView>
                        </View>
                      );
                    })}
                    {/* <View
                      style={{
                        ...style.NSActionIconBox,
                        paddingStart: shouldApplyLandscapeLayout ? 0 : 16,
                        marginBottom: shouldApplyLandscapeLayout ? 8 : 0,
                        alignItems: 'center',
                      }}> */}
                    {/* <LikeBtnComponent
                        inActiveIcon={LIKE_ICON}
                        activeIcon={LIKE_ICON}
                        inActiveTintColor={theme.colors.fullScreenIconTintColor}
                        disable={likeBtnDisable || !unlockPost}
                        likeButtonPress={() =>
                          likeIconBtnClick(REACTION_LIKE, likeCount, postIsLike)
                        }
                        isLike={postIsLike}
                        style={style.reelIcon}
                      /> */}
                    {/* {showLikeCountBox ? (
                        <EntutoTextView
                          style={{...style.NSActionCountTxt, color: '#FFFFFF'}}>
                          {formatLikeNumber(likeCount)}
                        </EntutoTextView>
                      ) : null} */}
                    {/* </View> */}
                  </View>

                  {/* Other action icons */}
                  <View
                    style={{
                      paddingStart: isLandscape ? 0 : 6,
                    }}>
                    <View
                      style={{
                        ...style.NSActionIconBox,
                        ...style.iconGap,
                        alignContent: 'center',
                        paddingStart: isLandscape ? 16 : 6,
                        marginBottom: isLandscape ? 6 : 16,
                        // alignItems: 'center',
                      }}>
                      <TouchableOpacity
                        disabled={commentBtnDisable || !unlockPost}
                        onPress={() => commentBtnPress(false)}
                        style={{flexDirection: 'row'}}>
                        <Image
                          style={{
                            ...style.reelIcon,
                            // tintColor: showCommentActive
                            //   ? theme.colors.postActiveIconColor
                            //   : theme.colors.fullScreenIconTintColor,
                          }}
                          source={COMMENT_ICON}
                          resizeMode="contain"
                        />
                      </TouchableOpacity>
                      <EntutoTextView
                        style={{
                          color: '#FFFFFF',
                          fontSize: theme.calculateFontSizeNew(10),
                          textAlign: 'center',
                          marginTop: 4,
                        }}>
                        20
                      </EntutoTextView>
                    </View>
                    <View
                      style={{
                        ...style.NSActionIconBox,
                        flexDirection: 'column',
                        alignItems: 'center',
                        marginBottom: shouldApplyLandscapeLayout ? 8 : 0,

                        paddingStart: isLandscape ? 16 : 16,
                        marginEnd: isLandscape ? 0 : 10,
                      }}>
                      {showBookmarkIcon ? (
                        <BookmarkBtnComponent
                          activeIcon={BOOKMARK_FILLED_ICON}
                          inActiveIcon={BOOKMARK_ICON}
                          // inActiveTintColor={
                          //   theme.colors.fullScreenIconTintColor
                          // }
                          disable={bookmarkBtnDisable || !unlockPost}
                          bookmarkButtonPress={() => bookmarkIconBtnClick()}
                          isBookmark={postIsBookmark}
                          style={style.reelIcon}
                        />
                      ) : null}
                    </View>
                    {/* <View style={{...style.NSActionIconBox, zIndex: 9}}>
                      <TouchableOpacity onPress={() => shareBtnPress()}>
                        <Image
                          style={{
                            ...style.NSActionIcon,
                            tintColor: theme.colors.fullScreenIconTintColor,
                          }}
                          source={SHARE_ICON}
                          resizeMode="contain"
                        />
                      </TouchableOpacity>
                    </View> */}
                  </View>
                </View>
                {showBMWarrings ? (
                  <View style={style.bookmarkWarringBox}>
                    <EntutoTextView style={style.bookmarkWarringTxt}>
                      Hey hey! This post is set to expire soon. Tell your
                      friends to check it out now!
                    </EntutoTextView>
                  </View>
                ) : null}
              </>
            ) : null}
            {/* <View style={style.NSProfileSection}>
                                <ScrollView >
                                    <View>
                                        <View style={style.NSProfileView}>
                                            <ProgressiveImage
                                                style={style.NSProfileViewImage}
                                                source={hasImageUrlExist(itemData.profile_picture) ? { uri: itemData.profile_picture } : null}
                                                defaultImageSource={ProfileImagePlaceholder}

                                            />
                                            <View>
                                                <TouchableOpacity onPress={() => goToProfile(itemData.profile_seq)}>
                                                    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                                                        <EntutoTextView style={style.NSProfileName}>{itemData.display_name}</EntutoTextView>
                                                        {
                                                            itemData.is_verified == "YES" ?
                                                                <Image
                                                                    style={style.verifiedIcon}
                                                                    source={VerifiedIcon}
                                                                    resizeMode={'contain'}
                                                                />
                                                                :
                                                                null
                                                        }
                                                    </View>

                                                </TouchableOpacity>
                                                <EntutoTextView style={style.NSProfileID}>{UserHandlePrefix}{itemData.user_handle}</EntutoTextView>
                                            </View>
                                        </View>
                                        <DescriptionCaptionStyle
                                            validHandleList={validUserHandleList}
                                            mainText={postComments}
                                            mainTextStyle={style.NSPostDescTxt}
                                            highlightStyle={defaultStyle.boldTagTxt}
                                        />
                                    </View>
                                </ScrollView>
                            </View> */}
            {/* {
                                unlockPost ?
                                    <View style={{ ...style.reelProgressBarContainer, zIndex: 1 }}>
                                        <ReelProgressBar value={videoCompletedPercentage.current} />
                                    </View>
                                    : null
                            } */}
          </View>
        ) : (
          <>
            {fullScreen ? (
              //{*********FULLSCREEN POSTS - IMAGE  POSTS*************}
              <View
                style={{
                  height: shouldApplyLandscapeLayout
                    ? Dimensions.screenWidth
                    : AVL_HEIGHT,
                  width: shouldApplyLandscapeLayout
                    ? AVL_HEIGHT
                    : Dimensions.screenWidth,
                }}>
                {/* {
                                            unlockPost ? */}
                <View
                  style={{
                    ...style.playListCountContainer,
                    zIndex: 1,
                    marginStart: 4,
                  }}>
                  <View style={style.NSProfileView}>
                    <View style={style.NSProfileView}>
                      {itemData.media_type == 'VIDEO' ? (
                        <>
                          {cameFrom == 'EPISODE' ? (
                            <View style={style.playListBox}>
                              <View style={style.playlistCircleBox} />
                              <Image
                                source={
                                  playlistLogo != null
                                    ? {uri: playlistLogo}
                                    : PlaylistPlaceholder
                                }
                                style={style.playlistCover}
                              />
                              <EntutoTextView style={style.playListCountText}>
                                {playlistSequence}
                              </EntutoTextView>
                            </View>
                          ) : (
                            <>
                              {showPlayViewIcon ? (
                                <View
                                  style={{
                                    ...style.NSActionIconBox,
                                    paddingStart: 0,
                                    marginTop: 8,
                                    flexDirection: 'row',
                                  }}>
                                  <Image
                                    style={{
                                      ...style.NSActionIcon,
                                      tintColor: '#FFFFFF',
                                      height: 24,
                                      width: 24,
                                    }}
                                    source={PlayViewCount}
                                    resizeMode="contain"
                                  />
                                  <EntutoTextView
                                    style={style.NSActionCountTxt}>
                                    {playViewCount}
                                  </EntutoTextView>
                                </View>
                              ) : null}
                            </>
                          )}
                        </>
                      ) : null}
                    </View>
                  </View>
                </View>
                {/* : null} */}
                {!blockPost ? (
                  <View style={{...style.NSTopHeaderThreeDotIcon, right: 8}}>
                    <TouchableOpacity
                      style={{paddingHorizontal: 10, paddingBottom: 10}}
                      onPress={() => threeDotMenuClick()}>
                      <Image
                        style={style.headerOptionIcon}
                        source={ThreeDotVerticalIcon}
                      />
                    </TouchableOpacity>
                  </View>
                ) : null}

                {blockPost ? (
                  <>
                    {blockMsg.length != 0 ? (
                      <BottomSheetSuccessMsg
                        successMsg={blockMsg}
                        showCloseBtn={false}
                      />
                    ) : null}
                    {blockMsgDB.length != 0 ? (
                      <View style={style.blockErrMsgMainBox}>
                        <View style={style.blockErrMsgBox}>
                          <EntutoTextView style={style.blockErrMsgHeading}>
                            Post Blocked
                          </EntutoTextView>
                          <EntutoTextView style={style.blockErrMsg}>
                            {blockMsgDB}
                          </EntutoTextView>
                        </View>
                      </View>
                    ) : null}
                  </>
                ) : null}
                {unlockPost ? (
                  <View
                    style={{flex: 1, flexDirection: 'column', width: '100%'}}>
                    {itemData.media_type == 'VIDEO' ? (
                      <>
                        {showDefaultVideoImage ? (
                          <View
                            style={{
                              backgroundColor: 'black',
                              height: '100%',
                              width: '100%',
                              alignItems: 'center',
                              justifyContent: 'center',
                            }}>
                            <ActivityIndicator
                              size={24}
                              style={{
                                backgroundColor: '#00000080',
                                zIndex: 3,
                                padding: 20,
                                borderRadius: 15,
                              }}
                            />
                            <View
                              style={{
                                position: 'absolute',
                                top: 0,
                                left: 0,
                                right: 0,
                                bottom: 0,
                                alignItems: 'center',
                                justifyContent: 'center',
                                zIndex: 1,
                              }}>
                              <ImageBackground
                                imageStyle={{borderRadius: 0}}
                                source={
                                  hasImageUrlExist(itemData.media_cover)
                                    ? {uri: itemData.media_cover}
                                    : null
                                }
                                style={style.videPostImage}></ImageBackground>
                            </View>
                          </View>
                        ) : null}
                      </>
                    ) : null}
                    {/*prolly POST IMAGE ?? */}
                    {itemData.media_type == 'IMAGE' ? (
                      <TapGestureHandler
                        waitFor={doubleTapRef}
                        onActivated={onSingleTap}>
                        <View style={style.postImageContainer}>
                          <TapGestureHandler
                            maxDelayMs={250}
                            ref={doubleTapRef}
                            numberOfTaps={2}
                            onActivated={onDoubleTap}>
                            <Animated.View>
                              <ImageBackground
                                imageStyle={{borderRadius: 0}}
                                source={
                                  hasImageUrlExist(itemData.media_file)
                                    ? {uri: itemData.media_file}
                                    : null
                                }
                                style={{
                                  ...style.NSmediaImage,
                                  height: AVL_HEIGHT,
                                }}
                                resizeMode="contain">
                                <ImageOverlay />
                                {/* <AnimatedImage
                                  source={HeartActive}
                                  style={[
                                    style.postImageContainerHeartIcon,
                                    {
                                      shadowOffset: {
                                        width: 0,
                                        height: Platform.OS == 'ios' ? 2 : 20,
                                      },
                                      shadowOpacity: 0.35,
                                      shadowRadius:
                                        Platform.OS == 'ios' ? 2 : 35,
                                    },
                                    rStyle,
                                  ]}
                                  resizeMode={'contain'}
                                /> */}
                              </ImageBackground>
                            </Animated.View>
                          </TapGestureHandler>
                        </View>
                      </TapGestureHandler>
                    ) : null}
                    {itemData.media_type == 'VIDEO' ? (
                      <TapGestureHandler
                        waitFor={doubleTapVideoRef}
                        onActivated={onSingleTapVideo}>
                        <View style={style.videoBox}>
                          <TapGestureHandler
                            maxDelayMs={250}
                            ref={doubleTapVideoRef}
                            numberOfTaps={2}
                            onActivated={onDoubleTap}>
                            <Animated.View>
                              <FullScreenPlayer
                                insets={insets}
                                videoPlayerRef={videoPlayerRef}
                                media_cover={itemData.media_cover}
                                media_file={itemData.media_file}
                                playVideo={!playVideo}
                                onLoadStart={() => reelsVideoStart()}
                                onLoad={() => videoLoadStart()}
                                onBuffer={() => onVideoLoaded()}
                                height={AVL_HEIGHT}
                                style={{
                                  ...style.NSPostVideo,
                                  height: AVL_HEIGHT,
                                }}
                                isLandscape={isLandscape}
                              />
                              {/* <Video
                                                                                ref={videoPlayerRef}
                                                                                poster={hasImageUrlExist(itemData.media_cover) ? itemData.media_cover : null}
                                                                                posterResizeMode={'cover'}
                                                                                source={{ uri: itemData.media_file }}
                                                                                resizeMode='contain'
                                                                                repeat={true}
                                                                                paused={!playVideo}
                                                                                muted={false}
                                                                                onLoadStart={() => reelsVideoStart()}
                                                                                onProgress={(e) => onVideoProgress(e)}
                                                                                onLoad={() => videoLoadStart()}
                                                                                onBuffer={() => onVideoLoaded()}
                                                                                // onError={(e) => console.log(e)}
                                                                                onEnd={() => onVideoEnd()}
                                                                                style={{ ...style.NSPostVideo, height: AVL_HEIGHT }}
                                                                            /> */}
                              <VideoOverlayReels />
                              {/* <AnimatedImage
                                source={HeartVideoActive}
                                style={[
                                  style.videPostHeartImage,
                                  {
                                    shadowOffset: {
                                      width: 0,
                                      height: Platform.OS == 'ios' ? 2 : 20,
                                    },
                                    shadowOpacity: 0.35,
                                    shadowRadius: Platform.OS == 'ios' ? 2 : 35,
                                    position: 'absolute',
                                    top: '40%',
                                    left: '35%',
                                    zIndex: 3,
                                  },
                                  rStyle,
                                ]}
                                resizeMode={'contain'}
                              /> */}
                              {videoLoading ? (
                                <View style={style.playBtnBox}>
                                  <ActivityIndicator size={32} />
                                </View>
                              ) : null}
                              {showPlayPauseBtn ? (
                                <View style={style.playBtnBox}>
                                  <Image
                                    style={style.playBtnBoxIcon}
                                    source={PlayBtnIconFilled}
                                    resizeMode="cover"
                                  />
                                </View>
                              ) : null}
                            </Animated.View>
                          </TapGestureHandler>
                        </View>
                      </TapGestureHandler>
                    ) : null}
                  </View>
                ) : (
                  <View style={{...style.lockBox, height: AVL_HEIGHT}}>
                    <ImageBackground
                      style={{...style.postFuzzayImage, height: AVL_HEIGHT}}
                      source={
                        hasImageUrlExist(itemData.fuzzy_image)
                          ? {uri: itemData.fuzzy_image}
                          : null
                      }>
                      <VideoOverlayReels />
                      <View style={{...style.lockBtnBox, top: '35%'}}>
                        <Image
                          style={{
                            ...style.lockBoxIcon,
                            height: 64,
                            width: 52,
                            marginBottom: 20,
                          }}
                          source={LockIcon}
                          resizeMode="contain"
                        />
                        {isPaidProfile ? (
                          <TouchableOpacity
                            style={style.lockButton}
                            onPress={() => unlockBtnClick()}>
                            <View
                              style={{
                                ...style.lockBtnTextBox,
                                alignItems: 'center',
                              }}>
                              <View>
                                <EntutoTextView style={style.lockButtonTxt}>
                                  {unlockMsg}
                                </EntutoTextView>
                              </View>
                              <View>
                                <EntutoTextView
                                  style={style.lockButtonPriceTxt}>
                                  {CurrencySymbol}
                                  {unlockFees}
                                </EntutoTextView>
                              </View>
                              <View>
                                <EntutoTextView style={style.lockButtonTxt}>
                                  {perMonthTxt}
                                </EntutoTextView>
                              </View>
                            </View>
                          </TouchableOpacity>
                        ) : (
                          <TouchableOpacity
                            style={style.lockButton}
                            onPress={() => unlockBtnClick()}>
                            <View style={style.lockBtnTextBox}>
                              <View>
                                <EntutoTextView style={style.lockButtonTxt}>
                                  {unlockMsg}
                                </EntutoTextView>
                              </View>
                              <View>
                                <EntutoTextView
                                  style={style.lockButtonPriceTxt}>
                                  {CurrencySymbol}
                                  {unlockFees}
                                </EntutoTextView>
                              </View>
                            </View>
                          </TouchableOpacity>
                        )}
                      </View>
                    </ImageBackground>
                  </View>
                )}

                {!blockPost ? ( //&& (!unlockPost|| !playVideo || itemData.media_type == "IMAGE")
                  <>
                    {/*{*********FULLSCREEN POSTS HEADER START*************}*/}

                    <View
                      style={{
                        ...style.likeBtnBox,
                        left: 0,
                        zIndex: 99,
                        bottom:
                          itemData.media_type == 'IMAGE'
                            ? 10
                            : BOTTOM_ACTION_BOX_GAP,
                      }}>
                      {/* FULLSCREEN IMAGE Profile and description content */}
                      <View style={{...style.reelProfileContainer}}>
                        <ProgressiveImage
                          style={style.NSProfileViewImage}
                          source={
                            hasImageUrlExist(itemData.profile_picture)
                              ? {uri: itemData.profile_picture}
                              : null
                          }
                          defaultImageSource={ProfileImagePlaceholder}
                          resizeMode="cover"
                        />
                        <View>
                          <View
                            style={{
                              flexDirection: 'row',
                              alignItems: 'center',
                              marginBottom: 6,
                            }}>
                            <TouchableOpacity
                              onPress={() => goToProfile(itemData.profile_seq)}
                              style={{
                                flexDirection: 'row',
                                alignItems: 'center',
                              }}>
                              <EntutoTextView
                                style={{
                                  ...style.headerDisplayName,
                                  color: '#FFFFFF',
                                }}>
                                {itemData.display_name}
                              </EntutoTextView>
                              {itemData.is_verified == 'YES' ? (
                                <Image
                                  style={style.verifiedIcon}
                                  source={VerifiedIcon}
                                  resizeMode={'contain'}
                                />
                              ) : null}
                            </TouchableOpacity>
                            <TouchableOpacity
                              onPress={() => followBtnPress()}
                              disabled={followServiceLoading}>
                              <View
                                style={{
                                  ...style.bellowFollowBtn,
                                  borderColor: 'white',
                                }}>
                                {followServiceLoading ? (
                                  <ActivityIndicator size={12} />
                                ) : (
                                  <EntutoTextView
                                    style={{
                                      ...style.bellowFollowBtnText,
                                      color: 'white',
                                    }}>
                                    {isFollowing ? 'FOLLOWING' : 'FOLLOW'}
                                  </EntutoTextView>
                                )}
                              </View>
                            </TouchableOpacity>
                          </View>
                          <EntutoTextView style={style.taggedText}>
                            <Text
                              style={{color: '#FFFFFF', fontWeight: 'bold'}}>
                              Cast:
                            </Text>
                            &nbsp;Siddhantkakar, prithvirajprasad ...
                          </EntutoTextView>
                        </View>
                      </View>
                      <View style={{...style.bellowDescription, width: '90%'}}>
                        <DescriptionCaptionStyle
                          validHandleList={validUserHandleList}
                          mainText="Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris."
                          mainTextStyle={{
                            ...style.bellowCommentText,
                            color: '#FFFFFF', // this will correctly override the theme color
                          }}
                          highlightStyle={defaultStyle.boldTagTxt}
                        />
                      </View>
                    </View>
                    {/*{*********FULLSCREEN POSTS HEADER END*************}*/}
                    {/* Reactions section on bottom right */}
                    <View
                      style={{
                        ...style.likeBtnBox,
                        right: 0,
                        zIndex: 99,
                        alignItems: 'flex-end',
                        bottom:
                          itemData.media_type == 'IMAGE'
                            ? 10
                            : BOTTOM_ACTION_BOX_GAP,
                      }}>
                      {/* Selected reactions moved to top */}
                      <View
                        style={{
                          marginBottom: 20,
                          flexDirection: 'row',
                          alignItems: 'center',
                        }}>
                        {selectedReactions.map((item, i) => {
                          let countValue = 0;
                          let isLike = false;
                          const dataObj = getValueFromReactions(item);
                          if (userReactions.hasOwnProperty(item)) {
                            countValue = userReactions[item].count;
                            if (userReactions[item].selected == 'YES') {
                              isLike = true;
                            }
                          }
                          if (dataObj != null) {
                            return (
                              <View
                                key={i}
                                style={{
                                  ...style.NSActionIconBox,
                                  ...style.iconGap,
                                  paddingStart: 16,
                                }}>
                                <LikeBtnComponent
                                  inActiveIcon={dataObj.icon}
                                  activeIcon={dataObj.icon}
                                  // inActiveTintColor={
                                  //   theme.colors.fullScreenIconTintColor
                                  // }
                                  disable={likeBtnDisable || !unlockPost}
                                  likeButtonPress={() =>
                                    likeIconBtnClick(item, countValue, isLike)
                                  }
                                  isLike={isLike}
                                  style={style.reelIcon}
                                />

                                {likeRemoveType.type == item ? (
                                  <EntutoTextView
                                    style={style.NSActionCountTxt}>
                                    {formatLikeNumber(countValue)}
                                  </EntutoTextView>
                                ) : null}
                              </View>
                            );
                          }
                        })}
                        <View
                          style={{...style.NSActionIconBox, paddingStart: 16}}>
                          {/* <LikeBtnComponent
                            inActiveIcon={LIKE_ICON}
                            activeIcon={LIKE_ICON}
                            // inActiveTintColor={
                            //   theme.colors.fullScreenIconTintColor
                            // }
                            disable={likeBtnDisable || !unlockPost}
                            likeButtonPress={() =>
                              likeIconBtnClick(
                                REACTION_LIKE,
                                likeCount,
                                postIsLike,
                              )
                            }
                            isLike={postIsLike}
                            style={style.reelIcon}
                          /> */}
                          {showLikeCountBox ? (
                            <EntutoTextView style={style.NSActionCountTxt}>
                              {formatLikeNumber(likeCount)}
                            </EntutoTextView>
                          ) : null}
                        </View>
                      </View>
                      <View style={{marginBottom: 20}}>
                        {hardcodedEmotions.map((emotion, i) => {
                          const isSelected = selectedEmotions.includes(
                            emotion.value,
                          );
                          return (
                            <View
                              key={i}
                              style={{
                                ...style.NSActionIconBox,
                                ...style.iconGap,
                                paddingStart: 16,
                                marginBottom: 20,
                                alignItems: 'center',
                              }}>
                              <TouchableOpacity
                                disabled={likeBtnDisable || !unlockPost}
                                onPress={() => onEmotionPress(emotion)}>
                                <Animated.View
                                  style={{
                                    transform: [
                                      {
                                        scale: getEmotionScaleAnimation(
                                          emotion.value,
                                        ),
                                      },
                                    ],
                                  }}>
                                  {emotion.lottiePath ? (
                                    <LottieView
                                      ref={ref => {
                                        if (ref) {
                                          emotionRefs.current[emotion.value] =
                                            ref;
                                        }
                                      }}
                                      source={emotion.lottiePath}
                                      style={style.reelIcon}
                                      loop={false}
                                      autoPlay={false}
                                    />
                                  ) : (
                                    <Image
                                      source={emotion.icon}
                                      style={[
                                        style.reelIcon,
                                        {
                                          tintColor: isSelected
                                            ? theme.colors.primaryColor
                                            : theme.colors
                                                .fullScreenIconTintColor,
                                        },
                                      ]}
                                      resizeMode="contain"
                                    />
                                  )}
                                </Animated.View>
                              </TouchableOpacity>
                              {/* Display reaction count below the icon */}
                              <EntutoTextView
                                style={{
                                  color: '#FFFFFF',
                                  fontSize: theme.calculateFontSizeNew(10),
                                  textAlign: 'center',
                                  marginTop: 4,
                                }}>
                                {emotion.count || 0}
                              </EntutoTextView>
                            </View>
                          );
                        })}
                        <View
                          style={{
                            ...style.NSActionIconBox,
                            paddingStart: shouldApplyLandscapeLayout ? 0 : 16,
                            marginBottom: shouldApplyLandscapeLayout ? 8 : 0,
                            alignItems: 'center',
                          }}>
                          {/* <LikeBtnComponent
                        inActiveIcon={LIKE_ICON}
                        activeIcon={LIKE_ICON}
                        inActiveTintColor={theme.colors.fullScreenIconTintColor}
                        disable={likeBtnDisable || !unlockPost}
                        likeButtonPress={() =>
                          likeIconBtnClick(REACTION_LIKE, likeCount, postIsLike)
                        }
                        isLike={postIsLike}
                        style={style.reelIcon}
                      /> */}
                          {/* {showLikeCountBox ? (
                        <EntutoTextView
                          style={{...style.NSActionCountTxt, color: '#FFFFFF'}}>
                          {formatLikeNumber(likeCount)}
                        </EntutoTextView>
                      ) : null} */}
                        </View>
                      </View>
                      {/* Other action buttons below reactions */}
                      <View
                        style={{
                          paddingStart: isLandscape ? 0 : 6,
                        }}>
                        <View
                          style={{
                            ...style.NSActionIconBox,
                            ...style.iconGap,
                            alignContent: 'center',
                            paddingStart: isLandscape ? 16 : 6,
                            marginBottom: isLandscape ? 6 : 16,
                            // alignItems: 'center',
                          }}>
                          <TouchableOpacity
                            disabled={commentBtnDisable || !unlockPost}
                            onPress={() => commentBtnPress(false)}
                            style={{flexDirection: 'row'}}>
                            <Image
                              style={{
                                ...style.reelIcon,
                                // tintColor: showCommentActive
                                //   ? theme.colors.postActiveIconColor
                                //   : theme.colors.fullScreenIconTintColor,
                              }}
                              source={COMMENT_ICON}
                              resizeMode="contain"
                            />
                          </TouchableOpacity>
                          <EntutoTextView
                            style={{
                              color: '#FFFFFF',
                              fontSize: theme.calculateFontSizeNew(10),
                              textAlign: 'center',
                              marginTop: 4,
                            }}>
                            20
                          </EntutoTextView>
                        </View>
                        <View
                          style={{
                            ...style.NSActionIconBox,
                            flexDirection: 'column',
                            alignItems: 'center',
                            marginBottom: shouldApplyLandscapeLayout ? 8 : 0,

                            paddingStart: isLandscape ? 16 : 16,
                            marginEnd: isLandscape ? 0 : 10,
                          }}>
                          {showBookmarkIcon ? (
                            <BookmarkBtnComponent
                              activeIcon={BOOKMARK_FILLED_ICON}
                              inActiveIcon={BOOKMARK_ICON}
                              // inActiveTintColor={
                              //   theme.colors.fullScreenIconTintColor
                              // }
                              disable={bookmarkBtnDisable || !unlockPost}
                              bookmarkButtonPress={() => bookmarkIconBtnClick()}
                              isBookmark={postIsBookmark}
                              style={style.reelIcon}
                            />
                          ) : null}
                        </View>
                        {/* <View style={{...style.NSActionIconBox, zIndex: 9}}>
                      <TouchableOpacity onPress={() => shareBtnPress()}>
                        <Image
                          style={{
                            ...style.NSActionIcon,
                            tintColor: theme.colors.fullScreenIconTintColor,
                          }}
                          source={SHARE_ICON}
                          resizeMode="contain"
                        />
                      </TouchableOpacity>
                    </View> */}
                      </View>
                    </View>
                    {showBMWarrings ? (
                      <View style={style.bookmarkWarringBox}>
                        <EntutoTextView style={style.bookmarkWarringTxt}>
                          Hey hey! This post is set to expire soon. Tell your
                          friends to check it out now!
                        </EntutoTextView>
                      </View>
                    ) : null}
                  </>
                ) : null}
                {/* {
                                            unlockPost && itemData.media_type == "VIDEO" ?
                                                <View style={{ ...style.reelProgressBarContainer, zIndex: 1 }}>
                                                    <ReelProgressBar value={videoCompletedPercentage.current} />
                                                </View>
                                                : null
                                        } */}
              </View>
            ) : (
              // {*********HOMESCREEN POSTS - IMAGE & REELS POSTS*************}

              <View style={{...style.postCard}}>
                <View style={style.bellowProfileBox}>
                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                    }}>
                    {/*HOMESCREEN HEADER Start*/}
                    <View
                      style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        flex: 1,
                      }}>
                      <ProgressiveImage
                        style={style.NSProfileViewImage}
                        source={
                          hasImageUrlExist(itemData.profile_picture)
                            ? {uri: itemData.profile_picture}
                            : null
                        }
                        defaultImageSource={ProfileImagePlaceholder}
                        resizeMode="cover"
                      />
                      <View style={{marginLeft: 10, flex: 1}}>
                        <View
                          style={{
                            flexDirection: 'row',
                            alignItems: 'center',
                            marginBottom: 6,
                          }}>
                          <TouchableOpacity
                            onPress={() => goToProfile(itemData.profile_seq)}
                            style={{
                              flexDirection: 'row',
                              alignItems: 'center',
                            }}>
                            <EntutoTextView style={style.headerDisplayName}>
                              {itemData.display_name}
                            </EntutoTextView>
                            {itemData.is_verified == 'YES' ? (
                              <Image
                                style={style.verifiedIcon}
                                source={VerifiedIcon}
                                resizeMode={'contain'}
                              />
                            ) : null}
                          </TouchableOpacity>
                          <TouchableOpacity
                            onPress={() => followBtnPress()}
                            disabled={followServiceLoading}>
                            <View style={style.bellowFollowBtn}>
                              {followServiceLoading ? (
                                <ActivityIndicator size={12} />
                              ) : (
                                <EntutoTextView
                                  style={style.bellowFollowBtnText}>
                                  {isFollowing ? 'FOLLOWING' : 'FOLLOW'}
                                </EntutoTextView>
                              )}
                            </View>
                          </TouchableOpacity>
                        </View>
                        <EntutoTextView style={style.taggedText}>
                          <Text
                            style={{
                              color: theme.colors.primaryTextColor,
                              fontWeight: 'bold',
                            }}>
                            Cast:
                          </Text>
                          &nbsp;Siddhantkakar, prithvirajprasad ...
                        </EntutoTextView>
                      </View>
                    </View>
                    {/*HEADER END*/}
                    {!blockPost ? (
                      <View style={style.headerOptionIconBox}>
                        <TouchableOpacity onPress={() => threeDotMenuClick()}>
                          <View style={{padding: 10}}>
                            <Image
                              style={style.headerOptionIconNew}
                              source={ThreeDotVerticalIcon}
                            />
                          </View>
                        </TouchableOpacity>
                      </View>
                    ) : null}
                  </View>
                </View>
                <View style={{...style.postImageBox, position: 'relative'}}>
                  {blockPost ? (
                    <>
                      {blockMsg.length != 0 ? (
                        <BottomSheetSuccessMsg
                          successMsg={blockMsg}
                          showCloseBtn={false}
                        />
                      ) : null}
                      {blockMsgDB.length != 0 ? (
                        <View style={style.blockErrMsgMainBox}>
                          <View style={style.blockErrMsgBox}>
                            <EntutoTextView style={style.blockErrMsgHeading}>
                              Post Blocked
                            </EntutoTextView>
                            <EntutoTextView style={style.blockErrMsg}>
                              {blockMsgDB}
                            </EntutoTextView>
                          </View>
                        </View>
                      ) : null}
                    </>
                  ) : null}

                  {unlockPost ? (
                    <View
                      style={{
                        flex: 1,
                        flexDirection: 'column',
                        width: '100%',
                        position: 'relative',
                      }}>
                      {itemData.media_type == 'IMAGE' ? (
                        <TapGestureHandler
                          waitFor={doubleTapRef}
                          onActivated={onSingleTap}>
                          <View
                            style={{
                              ...style.postImageContainer,

                              position: 'relative',
                            }}>
                            <TapGestureHandler
                              maxDelayMs={250}
                              ref={doubleTapRef}
                              numberOfTaps={2}
                              onActivated={onDoubleTap}>
                              <Animated.View>
                                <ImageBackground
                                  source={
                                    hasImageUrlExist(itemData.media_file)
                                      ? {uri: itemData.media_file}
                                      : null
                                  }
                                  style={style.postImageContainerImg}>
                                  {/* <AnimatedImage
                                    source={HeartActive}
                                    style={[
                                      style.postImageContainerHeartIcon,
                                      {
                                        shadowOffset: {
                                          width: 0,
                                          height: Platform.OS == 'ios' ? 2 : 20,
                                        },
                                        shadowOpacity: 0.35,
                                        shadowRadius:
                                          Platform.OS == 'ios' ? 2 : 35,
                                      },
                                      rStyle,
                                    ]}
                                    resizeMode={'contain'}
                                  /> */}
                                </ImageBackground>
                              </Animated.View>
                            </TapGestureHandler>
                          </View>
                        </TapGestureHandler>
                      ) : null}
                      {itemData.media_type == 'VIDEO' ? (
                        <TapGestureHandler
                          waitFor={doubleTapVideoRef}
                          onActivated={onSingleTapVideo}>
                          <View style={{...style.videoBox}}>
                            <TapGestureHandler
                              maxDelayMs={250}
                              ref={doubleTapVideoRef}
                              numberOfTaps={2}
                              onActivated={onDoubleTap}>
                              <Animated.View>
                                <ImageBackground
                                  source={
                                    hasImageUrlExist(itemData.media_cover)
                                      ? {uri: itemData.media_cover}
                                      : null
                                  }
                                  style={style.videPostImage}>
                                  {/* <ImageOverlayNew /> */}
                                  <View style={style.playBtnBox}>
                                    <Image
                                      style={{
                                        height: 80,
                                        width: 80,
                                        alignSelf: 'center',
                                      }}
                                      source={PlayBtnIconFilled}
                                      resizeMode="contain"
                                    />
                                  </View>
                                  {/* <AnimatedImage
                                    source={HeartVideoActive}
                                    style={[
                                      style.videPostHeartImage,
                                      {
                                        shadowOffset: {
                                          width: 0,
                                          height: Platform.OS == 'ios' ? 2 : 20,
                                        },
                                        shadowOpacity: 0.35,
                                        shadowRadius:
                                          Platform.OS == 'ios' ? 2 : 35,
                                      },
                                      rStyle,
                                    ]}
                                    resizeMode={'contain'}
                                  /> */}
                                </ImageBackground>
                              </Animated.View>
                            </TapGestureHandler>
                          </View>
                        </TapGestureHandler>
                      ) : null}
                      {showTagUser ? (
                        <View
                          style={{...style.tagPIconBox, bottom: 32, right: 16}}>
                          <TouchableOpacity
                            onPress={() => postTagPeoplePress()}>
                            <Image
                              style={style.tagPIconBoxIcon}
                              source={TagProfileIcon}
                              resizeMode="cover"
                            />
                          </TouchableOpacity>
                        </View>
                      ) : null}
                    </View>
                  ) : (
                    <View style={style.lockBox}>
                      <Image
                        style={{...style.postFuzzayImage, borderRadius: 1}}
                        source={
                          hasImageUrlExist(itemData.fuzzy_image)
                            ? {uri: itemData.fuzzy_image}
                            : null
                        }
                      />
                      <ImageOverlayNew />
                      {/* <LinearGradient colors={['#FFFFFFE5', '#FFFFFF33']}
                                                            start={{ x: 0, y: 0 }} end={{ x: 1, y: 0 }}
                                                            style={style.linearGradient} /> */}
                      <View style={{...style.lockBtnBox, top: '32%'}}>
                        <Image
                          style={{
                            ...style.lockBoxIcon,
                            width: 64,
                            height: 53,
                            marginBottom: 22,
                            borderRadius: 1,
                          }}
                          source={LockIcon}
                          resizeMode="contain"
                        />
                        {isPaidProfile ? (
                          <TouchableOpacity
                            style={style.lockButton}
                            onPress={() => unlockBtnClick()}>
                            <View
                              style={{
                                ...style.lockBtnTextBox,
                                alignItems: 'center',
                              }}>
                              <View>
                                <EntutoTextView style={style.lockButtonTxt}>
                                  {unlockMsg}
                                </EntutoTextView>
                              </View>
                              <View>
                                <EntutoTextView
                                  style={style.lockButtonPriceTxt}>
                                  {CurrencySymbol}
                                  {unlockFees}
                                </EntutoTextView>
                              </View>
                              <View>
                                <EntutoTextView style={style.lockButtonTxt}>
                                  {perMonthTxt}
                                </EntutoTextView>
                              </View>
                            </View>
                          </TouchableOpacity>
                        ) : (
                          <TouchableOpacity
                            style={style.lockButton}
                            onPress={() => unlockBtnClick()}>
                            <View
                              style={{
                                ...style.lockBtnTextBox,
                                alignItems: 'center',
                              }}>
                              <View>
                                <EntutoTextView style={style.lockButtonTxt}>
                                  {unlockMsg}
                                </EntutoTextView>
                              </View>
                              <View>
                                <EntutoTextView
                                  style={style.lockButtonPriceTxt}>
                                  {CurrencySymbol}
                                  {unlockFees}
                                </EntutoTextView>
                              </View>
                            </View>
                          </TouchableOpacity>
                        )}
                      </View>
                    </View>
                  )}
                </View>
                <View style={style.mainProfileBoxContainer}>
                  {!blockPost ? (
                    <>
                      <View
                        style={{
                          ...style.postActionIconBox,
                          paddingHorizontal: 10,
                          // Add these Flexbox properties for even spacing
                          flexDirection: 'row', // Arrange children in a row
                          justifyContent: 'space-around', // Distribute items evenly in the container
                          alignItems: 'center', // Align items vertically in the center
                        }}>
                        {hardcodedEmotions.map((emotion, i) => {
                          const isSelected = selectedEmotions.includes(
                            emotion.value,
                          );
                          return (
                            <View
                              key={i}
                              style={
                                {
                                  // ...style.postActionTextIconBox,
                                }
                              }>
                              <TouchableOpacity
                                disabled={likeBtnDisable}
                                onPress={() => onEmotionPress(emotion)}>
                                <Animated.View
                                  style={{
                                    transform: [
                                      {
                                        scale: getEmotionScaleAnimation(
                                          emotion.value,
                                        ),
                                      },
                                    ],
                                  }}>
                                  {emotion.lottiePath ? (
                                    <LottieView
                                      ref={ref => {
                                        if (ref) {
                                          emotionRefs.current[
                                            `home_${emotion.value}`
                                          ] = ref;
                                        }
                                      }}
                                      source={emotion.lottiePath}
                                      style={style.listPostIcon}
                                      loop={false}
                                      autoPlay={false}
                                    />
                                  ) : (
                                    <Image
                                      source={emotion.icon}
                                      style={[
                                        style.listPostIcon,
                                        {
                                          tintColor: isSelected
                                            ? theme.colors.primaryColor
                                            : theme.colors
                                                .postInActiveIconColor,
                                        },
                                      ]}
                                      resizeMode="contain"
                                    />
                                  )}
                                </Animated.View>
                              </TouchableOpacity>
                              {/* Display reaction count below the icon */}
                              <EntutoTextView
                                style={{
                                  color: theme.colors.primaryTextColor,
                                  fontSize: theme.calculateFontSizeNew(8),
                                  textAlign: 'center',
                                  marginTop: 2,
                                }}>
                                {emotion.count || 0}
                              </EntutoTextView>
                            </View>
                          );
                        })}

                        {/* <View
                          style={{
                            ...style.postActionTextIconBox,
                           
                          }}> */}
                        {/* <LikeBtnComponent
      inActiveIcon={LIKE_ICON}
      activeIcon={LIKE_ICON}
      disable={likeBtnDisable}
      likeButtonPress={() =>
        likeIconBtnClick(
          REACTION_LIKE,
          likeCount,
          postIsLike,
        )
      }
      isLike={postIsLike}
      style={style.listPostIcon}
    /> */}
                        {/* {showLikeCountBox ? (
                            <EntutoTextView
                              style={{
                                ...style.postActionText,
                                ...style.newCountText,
                              }}>
                              {formatLikeNumber(likeCount)}
                            </EntutoTextView>
                          ) : null} */}
                        {/* <EntutoTextView style={{ ...style.postActionText, ...style.newCountText }}>{likeCount}</EntutoTextView> */}
                        {/* </View> */}
                        <View
                          style={{
                            ...style.postActionTextIconBox,
                            paddingTop: 6,
                          }}>
                          <TouchableOpacity
                            disabled={commentBtnDisable || !unlockPost}
                            onPress={() => commentBtnPress(false)}
                            style={{flexDirection: 'row'}}>
                            <Image
                              style={{
                                ...style.reelIcon,
                                // tintColor: showCommentActive
                                //   ? theme.colors.postActiveIconColor
                                //   : theme.colors.fullScreenIconTintColor,
                              }}
                              source={COMMENT_ICON}
                              resizeMode="contain"
                            />
                          </TouchableOpacity>
                          <EntutoTextView
                            style={{
                              color: '#FFFFFF',
                              fontSize: theme.calculateFontSizeNew(10),
                              textAlign: 'center',
                              marginTop: 4,
                            }}>
                            20
                          </EntutoTextView>
                        </View>
                        <View
                          style={{
                            paddingTop: 6,
                          }}>
                          {showBookmarkIcon ? (
                            <BookmarkBtnComponent
                              activeIcon={BOOKMARK_FILLED_ICON}
                              inActiveIcon={BOOKMARK_ICON}
                              // inActiveTintColor={
                              //   theme.colors.fullScreenIconTintColor
                              // }
                              disable={bookmarkBtnDisable || !unlockPost}
                              bookmarkButtonPress={() => bookmarkIconBtnClick()}
                              isBookmark={postIsBookmark}
                              style={style.reelIcon}
                            />
                          ) : null}
                        </View>
                        {/* <View style={style.postActionTextIconBox}> */}
                        {/* <TouchableOpacity onPress={() => shareBtnPress()}>
        <Image
          style={style.postActionCommentIcon}
          source={SHARE_ICON}
          resizeMode="contain"
        /> */}
                        {/* <SharePostIcon style={style.postActionIcon} /> */}
                        {/* </TouchableOpacity> */}
                        {/* <EntutoTextView style={{ ...style.postActionText, ...style.newCountText }}>{shareCount}</EntutoTextView> */}
                        {/* </View> */}
                      </View>

                      {/* Description section - moved here */}
                      <View style={{marginTop: 8}}>
                        <View style={{flexGrow: 1}}>
                          {checkValueLength(postComments) ? (
                            <View style={style.bellowDescription}>
                              <DescriptionCaptionStyle
                                validHandleList={validUserHandleList}
                                mainText="ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris."
                                mainTextStyle={style.bellowCommentText}
                                highlightStyle={defaultStyle.boldTagTxt}
                              />
                            </View>
                          ) : (
                            <View style={style.bellowDescription}>
                              <DescriptionCaptionStyle
                                validHandleList={validUserHandleList}
                                mainText="ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris."
                                mainTextStyle={style.bellowCommentText}
                                highlightStyle={defaultStyle.boldTagTxt}
                              />
                            </View>
                          )}
                        </View>
                        <View
                          style={{
                            ...style.bellowTimeFieldTextBox,
                          }}>
                          <EntutoTextView style={style.bellowTimeFieldText}>
                            {itemData.posted_on}
                          </EntutoTextView>
                        </View>
                      </View>

                      {showBMWarrings ? (
                        <View style={style.bookmarkWarringBox}>
                          <EntutoTextView style={style.bookmarkWarringTxt}>
                            Hey hey! This post is set to expire soon. Tell your
                            friends to check it out now!
                          </EntutoTextView>
                        </View>
                      ) : null}
                    </>
                  ) : null}
                </View>
              </View>
            )}
          </>
        )}
        {openCommentPopup ? (
          <CommentComponent
            refreshKey={openCommentPopupKey}
            openPopup={openCommentPopup}
            postSeq={itemData.post_seq}
            postProfileSeq={itemData.profile_seq}
            navigation={navigation}
            isSheetComment={true}
            commentComponentClick={commentComponentClick}
          />
        ) : null}
      </BottomSheetModalProvider>
      <CustomSnackbar
        snackMsg={SnackbarMsg}
        snackType={snackBarType}
        displaySnackbar={displaySnackbar}
        refreshSnack={refreshSnackBar}
      />

      <ActionSheet
        ref={unlockSheetRef}
        // statusBarTranslucent
        bounceOnOpen={false}
        onClose={() => closeActionSheet()}
        closeOnPressBack={false}
        gestureEnabled={false}
        closeOnTouchBackdrop={false}
        defaultOverlayOpacity={0.3}
        openAnimationSpeed={8}>
        <ScrollView
          nestedScrollEnabled={true}
          onMomentumScrollEnd={() =>
            unlockSheetRef.current?.handleChildScrollEnd()
          }
          style={{backgroundColor: theme.colors.backgroundColor}}>
          <UnlockPostActionView
            postSeq={itemData.post_seq}
            profileSeq={itemData.profile_seq}
            amountValue={unlockFeesValue}
            refVal={unlockSheetRef}
            isPaidProfile={isPaidProfile}
            unlockPostActionClick={(clickId, obj) =>
              unlockPostActionClick(clickId, obj)
            }
          />
        </ScrollView>
      </ActionSheet>
      <ActionSheet
        ref={threeDotMenuSheetRef}
        statusBarTranslucent
        bounciness={4}
        gestureEnabled={false}
        defaultOverlayOpacity={0.3}
        openAnimationSpeed={8}
        onClose={() => {
          setPlayVideo(prevState => true);
          setShowPlayPauseBtn(prevState => false);
        }}>
        <ThreeDotMenuActionView
          navigation={navigation}
          isMyProfile={isMyProfile}
          unlockPost={unlockPost}
          postSeq={itemData.post_seq}
          ThreeDotMenuPress={(clickId, obj) => ThreeDotMenuPress(clickId, obj)}
          cameFrom={cameFrom}
        />
      </ActionSheet>
      {showConfirmPopup && (
        <ConfirmationPopup
          visiblePopupKey={showConfirmPopupKey}
          visiblePopup={showConfirmPopup}
          title={confirmTitle}
          messagebody={confirmMsg}
          positiveButton="Yes"
          negativeButton="No"
          data={warringsData}
          popupClick={(clickID, data) => {
            confirmPopupPress(clickID, data);
          }}
        />
      )}
      {/* They Want to remove this at 09-09-24  */}
      {/* <Modal
                animationType="slide"
                transparent
                visible={displaySharePopup}
                onRequestClose={() => setDisplaySharePopup(false)}
            >
                <SharePostProfileFeature shareBody1={shareBody1}
                    shareBody2={shareBody2}
                    shareType="POST"
                    shareSeq={itemData.post_seq}
                    sharePostProfileCallback={sharePostProfileCallback} />
            </Modal> */}

      <ActionSheet
        ref={updateUserLocationRef}
        statusBarTranslucent
        bounciness={4}
        gestureEnabled={false}
        defaultOverlayOpacity={0.3}
        openAnimationSpeed={8}>
        <ScrollView
          nestedScrollEnabled={true}
          onMomentumScrollEnd={() =>
            updateUserLocationRef.current?.handleChildScrollEnd()
          }
          style={{backgroundColor: theme.colors.backgroundColor}}>
          <UpdateUserLocationComponent
            navigation={navigation}
            updateUserLocationPopupPress={(clickId, obj) =>
              updateUserLocationPopupPress(clickId, obj)
            }
          />
        </ScrollView>
      </ActionSheet>
    </View>
  );
};

export default React.memo(PostCard);

const styles = theme =>
  StyleSheet.create({
    postCard: {
      flexDirection: 'column',
      position: 'relative',
    },
    bottomProfileAndReactionsContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: 14,
      marginBottom: 10,
    },
    bottomProfileContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
    },
    bottomReactionsContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'flex-end',
    },
    reelProfileContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 16,
    },
    reelReactionsContainer: {
      flexDirection: 'column',
      alignItems: 'flex-end',
      marginBottom: 20,
    },
    reelOtherActionsContainer: {
      flexDirection: 'column',
      alignItems: 'center',
    },
    postCardHeader: {
      flexDirection: 'row',
      flex: 1,
      alignItems: 'center',
      paddingHorizontal: 4,
    },
    newPostHeader: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      zIndex: 999,
      backgroundColor: '#00000001',
      minHeight: 64,
      paddingTop: 8,
    },
    headerImageBox: {
      height: 31.5,
      width: 31.5,
      marginRight: 10,
      borderRadius: 31.5,
      marginLeft: 18,
    },
    headerImage: {
      height: 31.5,
      width: 31.5,
      borderRadius: 31.5,
    },
    verifiedIcon: {
      width: 13,
      height: 13,
      marginLeft: 8,
      // position: 'absolute',
      // right: -4,
      // top: 12,
    },
    headerTextBox: {
      flexDirection: 'column',
      alignContent: 'center',
    },
    headerDisplayName: {
      fontSize: theme.calculateFontSizeNew(
        theme.dimensions.postProfileNameText,
      ), //12
      color: theme.colors.primaryTextColor, //#
      flexDirection: 'row',
      fontFamily: theme.getFontFamily('bold'),
    },
    headerOptionIconBox: {
      marginLeft: 'auto',
      flexDirection: 'row',
      alignItems: 'center',
    },
    headerOptionIcon: {
      width: 18,
      height: 18,
      resizeMode: 'contain',
      tintColor: '#FFFFFF',
      zIndex: 1000,
    },
    headerOptionIconNew: {
      width: 18,
      height: 14,
      resizeMode: 'contain',
      tintColor: theme.colors.postInActiveIconColor,
      zIndex: 1000,
    },
    postImageBox: {
      flex: 1,
      position: 'relative',
    },
    postImageContainer: {
      width: '100%',
      minHeight: 450,
      maxHeight: '100%',
      resizeMode: 'cover',
      position: 'relative',

      // paddingVertical: 1
    },
    postImageContainerImg: {
      width: '100%',
      height: '100%',
      resizeMode: 'cover',
      alignItems: 'center',
      justifyContent: 'center',
    },
    postImageContainerHeartIcon: {
      width: 150,
      height: 150,
      minHeight: 150,
      maxHeight: 150,
    },
    postImage: {
      width: '100%',
      // minHeight: IMAGE_HEIGHT,
      flex: 1,
      // minHeight: 300,
      borderRadius: 15,
    },
    postFuzzayImage: {
      width: '100%',
      height: '100%',
      minHeight: IMAGE_HEIGHT,
      resizeMode: 'cover',
      borderRadius: 1,
    },
    videPostImage: {
      width: '100%',
      height: '100%', //400
      minHeight: IMAGE_HEIGHT,
      // resizeMode: 'cover',
      alignItems: 'center',
      justifyContent: 'center',
    },
    videPostHeartImage: {
      width: 120,
      height: 120,
      minHeight: 120,
      maxHeight: 120,
    },
    postActionIconBox: {
      flexDirection: 'row',
      alignItems: 'center',
      // backgroundColor: '#FFF',
      justifyContent: 'space-around',
    },

    postActionIcon: {
      width: 22,
      height: 22,
    },
    postActionIconNew: {
      width: 18,
      height: 18,
    },
    postActionCommentIcon: {
      width: 22,
      height: 22,
      tintColor: theme.colors.primaryColor,
    },
    postActionTextIconBox: {
      flexDirection: 'column',
      // marginRight: 20,
      alignItems: 'center',
      justifyContent: 'center',
    },
    postActionText: {
      marginLeft: 6,
    },
    newCountText: {
      fontSize: theme.calculateFontSize(theme.dimensions.postActionCountText),
      color: '#FFF',

      // color: '#707070',
      marginLeft: 0,
      // marginTop: -4
    },
    postDescTxtBox: {
      marginBottom: 8,
      paddingHorizontal: 4,
    },
    videoBox: {
      width: '100%',
      minHeight: 450,
      maxHeight: '100%',
      resizeMode: 'cover',
      borderRadius: 1,
      position: 'relative',
      backgroundColor: '#FFF',
    },
    playBtnBox: {
      height: 80,
      width: 80,
      position: 'absolute',
      top: '50%',
      left: '50%',
      transform: [{translateX: -31.5}, {translateY: -31.5}],
      // backgroundColor: '#00000080',
      borderRadius: 15,
      justifyContent: 'center',
      alignItems: 'center',
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 1,
      },
      shadowOpacity: 0.5,
      shadowRadius: 1.41,

      // elevation: 2,
    },
    playBtnBoxIcon: {
      height: 80,
      width: 80, // tintColor: '#000',
      backgroundColor: 'transparent',
    },
    linearGradient: {
      position: 'absolute',
      top: 0,
      left: 0,
      bottom: 0,
      right: 0,
      borderRadius: 1,
    },

    lockBox: {
      width: '100%',
      minHeight: 450,
      resizeMode: 'cover',
      borderRadius: 15,
      position: 'relative',
    },
    lockBtnBox: {
      position: 'absolute',
      top: '25%',
      left: 0,
      right: 0,
      alignItems: 'center',
    },
    lockBoxIcon: {
      width: 34,
      height: 40,
    },
    lockButton: {
      borderRadius: 1,
      // backgroundColor: '#FFFFFF',
      backgroundColor: 'rgba(0, 0, 0, 0.2)',
      borderColor: '#FFFFFF',
      borderWidth: 2,
      zIndex: 20,
      elevation: 2,
    },
    lockBtnTextBox: {
      flexDirection: 'row',
      paddingHorizontal: 16,
      paddingVertical: 15,
    },
    lockButtonTxt: {
      color: '#FFFFFF',
      fontSize: theme.calculateFontSize(theme.dimensions.postLockBtnText),
      fontFamily: theme.getFontFamily('bold'),
    },
    lockButtonPriceTxt: {
      color: '#FFFFFF',
      fontSize: theme.calculateFontSize(theme.dimensions.postLockBtnText),
      fontFamily: theme.getFontFamily('bold'),
    },
    blockErrMsgMainBox: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: Colors.backgroundColor,
      zIndex: 2,
    },
    blockErrMsgBox: {
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      paddingHorizontal: 15,
      marginBottom: 15,
    },
    blockErrMsgHeading: {
      fontSize: theme.calculateFontSize(
        theme.dimensions.postBlockErrorHeadingText,
      ),
      fontWeight: '600',
      paddingHorizontal: 15,
      color: Colors.primaryColor,
    },
    blockErrMsg: {
      fontSize: theme.calculateFontSize(theme.dimensions.postBlockErrorMsgText),
      paddingHorizontal: 15,
      color: Colors.errorColor,
    },
    bookmarkWarringBox: {
      borderRadius: 8,
      paddingHorizontal: 8,
      paddingVertical: 8,
      backgroundColor: '#ff323250',
      marginHorizontal: 14,
    },
    bookmarkWarringTxt: {
      color: '#FFF',
      fontSize: theme.calculateFontSize(
        theme.dimensions.postBookmarkWarringText,
      ),
    },
    tagPIconBox: {
      position: 'absolute',
      right: 10,
      bottom: 10,
      backgroundColor: '#FFF',
      borderRadius: 32,
    },
    tagPIconBoxIcon: {
      width: 28,
      height: 28,
    },

    NSContainer: {
      backgroundColor: '#000000',
      // height: Dimensions.screenHeight - 100,
      width: Dimensions.screenWidth,
      position: 'relative',
      flexDirection: 'row',
    },
    NSmediaImage: {
      width: Dimensions.screenWidth,
      // height: Dimensions.screenHeight - StatusBar.currentHeight,
      backgroundColor: '#000',
      zIndex: 2,
      alignItems: 'center',
      justifyContent: 'center',
    },
    NSLinearGradient: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
    },
    NSBottomActionView: {
      position: 'absolute',
      bottom: 0,
      left: 0,
      right: 0,
      zIndex: 3,
    },
    NSBottomActionViewBox: {
      height: 56,
      position: 'relative',
      zIndex: 3,
      alignItems: 'center',
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    NSLikeCmntBox: {
      flex: 3,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: 14,
    },
    NSActionIconBox: {
      // flexDirection: 'row',
      //
      paddingEnd: 16,
      alignItems: 'center',
      justifyContent: 'center',
    },
    BNSActionIconBox: {
      flexDirection: 'row',
      paddingStart: 30,
      paddingEnd: 24,
      alignItems: 'center',
    },
    NSActionIcon: {
      width: 24,
      height: 24,
      marginRight: 6,
    },
    reelIcon: {
      width: 30,
      height: 30,
      marginRight: 0,
    },
    fullScreenIcon: {
      width: 32,
      height: 32,
      marginRight: 6,
    },
    listPostIcon: {
      width: 40,
      height: 40,
    },
    NSActionCountTxt: {
      fontSize: theme.calculateFontSize(theme.dimensions.postNSActionCountText),
      color: '#000000',
      marginStart: 6,
    },
    NSLinearGradBox: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
    },
    NSProfileSection: {
      position: 'absolute',
      bottom: 56,
      left: 30,
      right: 48,
      minHeight: 100,
      maxHeight: 120,
    },
    NSProfileView: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingTop: Platform.OS == 'ios' ? 40 : 20,
    },
    NSProfileViewImage: {
      width: 38,
      height: 38,
      borderRadius: 38,
      marginRight: 10,
    },
    NSProfileName: {
      color: '#000000',
      fontSize: theme.calculateFontSize(
        theme.dimensions.postFullScreenProfileNameText,
      ),
    },
    NSProfileID: {
      color: '#000000',
      fontSize: theme.calculateFontSize(
        theme.dimensions.postFullScreenProfileIDText,
      ),
      marginTop: 5,
    },
    NSPostDescTxt: {
      marginTop: 12,
      color: '#000000',
      fontSize: theme.calculateFontSize(
        theme.dimensions.postFullScreenProfileDescText,
      ),
    },
    NSPostVideo: {
      // height: Dimensions.screenHeight,
      // width: Dimensions.screenWidth, // Width is now handled dynamically in FullScreenPlayer
    },

    addCommentBox: {
      position: 'absolute',
      bottom: 0,
      left: 0,
      backgroundColor: '#FFFF',
      minHeight: 76,
      paddingVertical: 5,
      flexDirection: 'row',
      width: Dimensions.screenWidth,
      alignItems: 'center',
      height: 56,
      borderColor: '#000',
      borderWidth: 2,
    },
    NSSLinearGradient: {
      position: 'absolute',
      left: 0,
      right: 0,
      top: 0,
      bottom: 0,
    },
    NSTopHeaderThreeDotIcon: {
      position: 'absolute',
      top: Platform.OS == 'ios' ? 40 : 32,
      right: 16,
      zIndex: 3,
    },
    videoOverlayGrad: {
      position: 'absolute',
      top: 0,
      bottom: 0,
      left: 0,
      right: 0,
    },
    mainProfileBoxContainer: {
      minHeight: 10,
      backgroundColor: theme.colors.backgroundColor,
      paddingTop: 14,
      paddingBottom: 14,
    },
    bellowProfileBox: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      padding: 8,
      paddingStart: 14,
      alignItems: 'center',
    },
    bellowDescription: {
      paddingTop: 10,
      paddingHorizontal: 14,
    },
    nameWithFollowBox: {
      flexDirection: 'row',
    },
    bellowFollowBtn: {
      paddingHorizontal: 10,
      paddingVertical: 2,
      borderRadius: 5,
      borderWidth: 1,
      borderColor: theme.colors.primaryTextColor,
      backgroundColor: 'transparent',
      justifyContent: 'center',
      alignItems: 'center',
      marginLeft: 16,
    },
    bellowFollowBtnText: {
      fontSize: theme.calculateFontSizeNew(theme.dimensions.postFollowBtnText), //9
      color: theme.colors.primaryTextColor,
    },
    bellowTimeFieldTextBox: {
      // paddingTop: 10,
      paddingHorizontal: 14,
      marginBottom: 12,
      // marginVertical: 6
    },
    bellowTimeFieldText: {
      fontSize: theme.calculateFontSizeNew(theme.dimensions.postDateText),
      color: theme.colors.primaryTextColor,
      opacity: 0.5,
    },
    bellowCommentText: {
      marginBottom: 12,
      // color: theme.colors.primaryTextColor,
      fontSize: theme.calculateFontSizeNew(theme.dimensions.postDescText),
      color: theme.colors.descriptionBoxText,
    },
    //New Reels
    videoResolutionContainer: {
      position: 'absolute',
      top: 26,
      right: 36,
      zIndex: 1,
    },
    reelProgressBarContainer: {
      position: 'absolute',
      bottom: Platform.OS == 'ios' ? 10 : 0,
      left: 0,
      right: 0,
    },
    likeBtnBox: {
      position: 'absolute',
      bottom: 30,
    },
    iconGap: {
      marginBottom: 0,
    },
    playListCountContainer: {
      position: 'absolute',
      top: 42,
      left: 16,
    },
    playListBox: {
      flexDirection: 'row',
      alignItems: 'center',
      marginTop: 6,
      paddingLeft: 32, // Increased to make room for the circle box
      position: 'relative',
      // borderWidth: 1,
      height: 32,
    },
    playlistCircleBox: {
      width: 26,
      height: 26,
      borderRadius: 13,
      backgroundColor: '#000000',
      position: 'absolute',
      left: 0,
      top: 3,
      borderWidth: 1,
      borderColor: '#707070',
      overflow: 'hidden',
      justifyContent: 'center',
      alignItems: 'center',
    },
    playlistCover: {
      width: 26,
      height: 26,
      resizeMode: 'cover',
      borderRadius: 13,
    },
    playListCountText: {
      marginLeft: 0, // Removed margin since we increased paddingLeft in playListBox
      color: '#000000',
    },
    taggedText: {
      fontSize: theme.calculateFontSizeNew(theme.dimensions.postDescText),
      color: theme.colors.primaryColor,
    },
    // For fullscreen reels and posts, text should be white
    fullscreenText: {
      color: '#FFFFFF',
    },
  });
