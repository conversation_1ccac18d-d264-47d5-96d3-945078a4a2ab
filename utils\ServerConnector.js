import appData from '../data/Data';
import NetInfo from '@react-native-community/netinfo';
import {_getAccessKey, _getUserCredential, _setAccessKey} from './AuthLogin';
import ErrorMessages from '../constants/ErrorMessages';

const UnauthActioCode = ['11:GET_CODE_VALUES', '11:SUBMIT_USER_FEEDBACK'];

class ServerConnector {
  constructor() {}

  postData(json_data, success, failed) {
    NetInfo.fetch().then(async state => {
      if (state.isConnected) {
        json_data['_access_key'] = appData.__access_key;

        let userDetails = appData._userDetails;
        if (userDetails != null) {
          if (userDetails.hasOwnProperty('_username')) {
            if (!json_data.hasOwnProperty('user_id')) {
              json_data['user_id'] = userDetails._username;
            }
          }
          if (userDetails.hasOwnProperty('_password')) {
            if (!json_data.hasOwnProperty('password')) {
              json_data['password'] = userDetails._password;
            }
          }
          if (userDetails.hasOwnProperty('_profile_seq')) {
            if (!json_data.hasOwnProperty('profile_seq')) {
              json_data['profile_seq'] = userDetails._profile_seq;
            }
          }
          if (userDetails.hasOwnProperty('_user_seq')) {
            if (!json_data.hasOwnProperty('user_seq')) {
              json_data['user_seq'] = userDetails._user_seq;
            }
          }
        }
        // if (appData.loggedUserName.length != 0) {
        // 	json_data["user_id"] = appData.loggedUserName;
        // 	json_data["password"] = appData.loggedPassword;
        // }

        const requestOptions = {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(json_data),
        };

        consoleData(
          getUrlPath(json_data._action_code) + '/' + json_data._action_code,
          'green',
        );
        consoleData('req=>' + requestOptions.body);

        try {
          const response = await fetch(
            getUrlPath(json_data._action_code),
            requestOptions,
          );
          if (response.status == 500) {
            failed(
              '500',
              'There is an issue detected at the server side.',
              null,
            );
          } else if (response.status != 200) {
            failed(
              '200',
              'Could not connect to the server. Please try again later.',
              null,
            );
          } else {
            try {
              const resultData = await response.json();
              consoleData(
                'Response for ' + json_data._action_code + ': ',
                'green',
              );
              consoleData(resultData);
              if (resultData._access_key && resultData._access_key.length > 0) {
                if (!UnauthActioCode.includes(json_data._action_code)) {
                  appData.__access_key = resultData._access_key;
                  _setAccessKey(resultData._access_key);
                }
              }
              if (resultData.status == 'SUCCESS') {
                if (success != null) {
                  success(resultData);
                }
              } else {
                if (failed != null) {
                  failed(resultData.err_cd, resultData.msg, resultData);
                }
              }
            } catch (error) {
              consoleData(
                'Response for ' + json_data._action_code + ': ',
                'green',
              );
              consoleData(error);
              failed('PARSE_ERROR', 'Data parse error.', null);
            }
          }
        } catch (error) {
          failed(
            'UNKNOWN_ERROR',
            'Could not reach server. (' + error + ')',
            null,
          );
        }

        // fetch(getUrlPath(json_data._action_code), requestOptions)
        // 	.then(response => response.json())
        // 	.then((data) => {

        // 		consoleData("Response for " + json_data._action_code + ": ");
        // 		consoleData(data);

        // 		//Exception Case for convert data to Json
        // 		if (typeof (data) === "string") {
        // 			data = JSON.parse(data);
        // 		}
        // 		try {

        // 			if (data._access_key && data._access_key.length > 0) {
        // 				if (!UnauthActioCode.includes(json_data._action_code)) {
        // 					appData.__access_key = data._access_key;
        // 					_setAccessKey(data._access_key);
        // 				}
        // 			}
        // 		} catch (err) { }

        // 		if (data.status == "SUCCESS") {
        // 			if (success != null) {
        // 				success(data);
        // 			}
        // 		}
        // 		else {
        // 			if (failed != null) {
        // 				failed(data.err_cd, data.msg, data);
        // 			}
        // 		}
        // 	}).catch((error) => {

        // 		consoleData("Response for " + json_data._action_code + ": ");
        // 		consoleData(error);

        // 		if (error == "TypeError: Failed to fetch") {
        // 			error = "Could not connect to the server. Please try again";
        // 		}
        // 		else if (error == "TypeError: Network request failed" ||
        // 			error == "[TypeError: Network request failed]") {
        // 			error = "Could not connect to the server due to network issue. Please try again";
        // 		}
        // 		else if (error == "SyntaxError: Unexpected token < in JSON at position 0" ||
        // 			error == "SyntaxError: JSON Parse error: Unrecognized token '<'" ||
        // 			error == "[SyntaxError: JSON Parse error: Unexpected token: <]" ||
        // 			error == "[SyntaxError: JSON Parse error: Unrecognized token '<']") {
        // 			error = "The server returned nothing!";
        // 		}
        // 		else {
        // 			error = "There was an error in the server";
        // 		}

        // 		if (failed != null) {
        // 			failed("FAILED", error, null);
        // 		}
        // 	});
      } else {
        if (failed != null) {
          failed('FAILED', 'Please check your Internet Connection  ', null);
        }
      }
    });
  }

  postDataMultiPart(dataHashMap, imageHasMap, success, failed) {
    NetInfo.fetch().then(state => {
      if (state.isConnected) {
        dataHashMap['_access_key'] = appData.__access_key;

        let userDetails = appData._userDetails;
        if (userDetails != null) {
          if (userDetails.hasOwnProperty('_username')) {
            if (!dataHashMap.hasOwnProperty('user_id')) {
              dataHashMap['user_id'] = encodeURIComponent(
                userDetails._username,
              );
            }
          }
          if (userDetails.hasOwnProperty('_password')) {
            if (!dataHashMap.hasOwnProperty('password')) {
              dataHashMap['password'] = encodeURIComponent(
                userDetails._password,
              );
            }
          }
          if (userDetails.hasOwnProperty('_profile_seq')) {
            if (!dataHashMap.hasOwnProperty('profile_seq')) {
              dataHashMap['profile_seq'] = encodeURIComponent(
                userDetails._profile_seq,
              );
            }
          }
          if (userDetails.hasOwnProperty('_user_seq')) {
            if (!dataHashMap.hasOwnProperty('user_seq')) {
              dataHashMap['user_seq'] = encodeURIComponent(
                userDetails._user_seq,
              );
            }
          }
        }

        // if (appData.loggedUserName.length != 0) {
        // 	dataHashMap["user_id"] = appData.loggedUserName;
        // 	dataHashMap["password"] = appData.loggedPassword;
        // }

        var formData = new FormData();

        var index = 0;
        for (var key in imageHasMap) {
          formData.append(
            imageHasMap[key].inputName,
            imageHasMap[key].imageData,
            imageHasMap[key].imageData.name,
          );
          // formData.append("file" + index, imageHasMap[key]);
          index++;
        }
        // Check For formdata is Empty or not
        // var isFormDataEmpty = true;
        // for (var p in formData) {
        // 	isFormDataEmpty = false;
        // }
        var parameters = '';
        for (var key in dataHashMap) {
          if (parameters.length != 0) {
            parameters += '&';
          }
          parameters += key + '=' + dataHashMap[key];
        }

        const requestOptions = {
          method: 'POST',
        };
        // Body Changes For formdata is Empty or not
        if (imageHasMap.length == 0) {
          requestOptions.body = null;
        } else {
          requestOptions.body = formData;
        }

        consoleData(getUrlPath(dataHashMap._action_code) + '?' + parameters);
        consoleData(requestOptions);
        fetch(
          getUrlPath(dataHashMap._action_code) + '?' + parameters,
          requestOptions,
        )
          .then(response => response.json())
          .then(data => {
            consoleData(
              'Response for ' + dataHashMap._action_code + ': ',
              'green',
            );
            consoleData(data);
            //Exception Case for convert data to Json
            if (typeof data === 'string') {
              data = JSON.parse(data);
            }
            try {
              if (data._access_key && data._access_key.length > 0) {
                if (!UnauthActioCode.includes(dataHashMap._action_code)) {
                  appData.__access_key = data._access_key;
                  _setAccessKey(data._access_key);
                }
              }
            } catch (err) {}
            if (data.status == 'SUCCESS') {
              if (success != null) {
                success(data);
              }
            } else {
              if (failed != null) {
                failed(data.err_cd, data.msg, data);
              }
            }
          })
          .catch(error => {
            consoleData(
              'Response for ' + dataHashMap._action_code + ': ',
              'green',
            );
            consoleData(error);
            if (error == 'TypeError: Failed to fetch') {
              error = 'Could not connect to the server. Please try again';
            } else if (
              error == 'TypeError: Network request failed' ||
              error == '[TypeError: Network request failed]'
            ) {
              error =
                'Could not connect to the server due to network issue. Please try again';
            } else if (
              error ==
                'SyntaxError: Unexpected token < in JSON at position 0' ||
              error ==
                "SyntaxError: JSON Parse error: Unrecognized token '<'" ||
              error == '[SyntaxError: JSON Parse error: Unexpected token: <]' ||
              error == "[SyntaxError: JSON Parse error: Unrecognized token '<']"
            ) {
              if (
                ['11:SUBMIT_STORY', '11:SUBMIT_POST'].includes(
                  dataHashMap._action_code,
                )
              ) {
                error = ErrorMessages.addPostMediaSizeErr;
              } else {
                error = 'The server returned nothing! ';
              }
            } else {
              error = 'There was an error in the server';
            }

            if (failed != null) {
              failed('FAILED', error, null);
            }
          });
      } else {
        if (failed != null) {
          failed('FAILED', 'Please check your Internet Connection  ', null);
        }
      }
    });
  }
}

function getUrlPath(_action_code) {
  return 'http://10.0.2.2:8000/sotrueapp/appservice'; // Local Server
  // return 'https://sotrue-backend-latest-7ga3.onrender.com/sotrueapp/appservice'; // Sotrue Test Server
  // return 'https://www.sotrue.club/sotrueapp/appservice';  // Production
}

function consoleData(data, color = 'default') {
  let colorCode;

  switch (color.toLowerCase()) {
    case 'green':
      colorCode = '\x1b[32m'; // Green
      break;
    case 'red':
      colorCode = '\x1b[31m'; // Red
      break;
    default:
      colorCode = '\x1b[0m'; // Reset/default
  }

  console.log(`${colorCode}%s\x1b[0m`, data);
}

export default ServerConnector;
