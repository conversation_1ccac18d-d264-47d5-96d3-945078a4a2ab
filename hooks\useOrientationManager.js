import { useEffect } from 'react';
import Orientation from 'react-native-orientation-locker';

/**
 * Custom hook to manage screen orientation
 * @param {boolean} allowRotation - Whether to allow rotation or lock to portrait
 * @param {Array} dependencies - Dependencies array for useEffect
 */
const useOrientationManager = (allowRotation = false, dependencies = []) => {
  useEffect(() => {
    if (allowRotation) {
      // Allow all orientations
      Orientation.unlockAllOrientations();
    } else {
      // Lock to portrait
      Orientation.lockToPortrait();
    }

    // Cleanup: Always reset to portrait when component unmounts
    return () => {
      Orientation.lockToPortrait();
    };
  }, dependencies);

  // Return orientation control functions for manual control if needed
  return {
    lockToPortrait: () => Orientation.lockToPortrait(),
    lockToLandscape: () => Orientation.lockToLandscape(),
    unlockAllOrientations: () => Orientation.unlockAllOrientations(),
  };
};

export default useOrientationManager;
