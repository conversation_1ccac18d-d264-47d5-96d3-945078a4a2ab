import {useEffect} from 'react';
import Orientation from 'react-native-orientation-locker';

/**
 * Custom hook to manage screen orientation
 * @param {boolean} allowRotation - Whether to allow rotation or lock to portrait
 */
const useOrientationManager = (allowRotation = false) => {
  useEffect(() => {
    if (allowRotation) {
      // Allow all orientations
      Orientation.unlockAllOrientations();
    } else {
      // Lock to portrait
      Orientation.lockToPortrait();
    }

    // Cleanup: Always reset to portrait when component unmounts
    return () => {
      Orientation.lockToPortrait();
    };
  }, [allowRotation]); // Only depend on allowRotation
};

export default useOrientationManager;
