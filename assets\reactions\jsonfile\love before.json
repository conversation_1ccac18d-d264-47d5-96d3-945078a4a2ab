{"nm": "love before 2", "h": 500, "w": 500, "meta": {"g": "@lottiefiles/toolkit-js 0.65.0", "tc": "#ffffff"}, "layers": [{"ty": 6, "nm": "hmm", "sr": 1, "st": -69, "op": 47, "ip": 25, "ln": "1176", "au": {"lv": {"a": 0, "k": [0, 0]}}, "refId": "1", "ind": 1}, {"ty": 2, "nm": "guide", "sr": 1, "st": 0, "op": 24, "ip": 0, "ln": "779", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [0, 500, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "refId": "2", "ind": 2}, {"ty": 2, "nm": "mouth", "sr": 1, "st": 0, "op": 24, "ip": 0, "ln": "777", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [65.688, 38.15]}, "s": {"a": 1, "k": [{"s": [77, 77, 98.718], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 0}, {"s": [100, 100, 100], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 6}]}, "p": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [238.163, 355.327, 0], "t": 0, "ti": [-8.079, 10.616, 0], "to": [0, 0, 0]}, {"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [250.921, 348.116, 0], "t": 2.66, "ti": [0, 0, 0], "to": [8.079, -10.616, 0]}, {"s": [252.163, 327.327, 0], "t": 6}]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "refId": "3", "ind": 3}, {"ty": 2, "nm": "right eyebrow", "sr": 1, "st": 0, "op": 24, "ip": 0, "ln": "775", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [34.026, 12.352]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [329.818, 144.827, 0], "t": 15, "ti": [-9.713, 11.138, 0], "to": [0, 0, 0]}, {"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [339.787, 121.138, 0], "t": 17.077, "ti": [0, 0, 0], "to": [9.713, -11.138, 0]}, {"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [366.818, 111.827, 0], "t": 18.462, "ti": [9.086, -8.129, 0], "to": [0, 0, 0]}, {"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [348.586, 140.871, 0], "t": 20.423, "ti": [0, 0, 0], "to": [-9.086, 8.129, 0]}, {"s": [329.818, 144.827, 0], "t": 22.154}]}, "r": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [0], "t": 15}, {"s": [360], "t": 22.154}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "refId": "4", "ind": 4}, {"ty": 2, "nm": "left eyebrow", "sr": 1, "st": 0, "op": 24, "ip": 0, "ln": "773", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [22.75, 13.148]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [196.647, 109.702, 0], "t": 15, "ti": [17.897, 10.203, 0], "to": [0, 0, 0]}, {"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [180.897, 93.203, 0], "t": 17.192, "ti": [0, 0, 0], "to": [-17.897, -10.203, 0]}, {"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [156.647, 96.202, 0], "t": 18.462, "ti": [-2.725, -4.679, 0], "to": [0, 0, 0]}, {"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [172.275, 106.821, 0], "t": 20.192, "ti": [0, 0, 0], "to": [2.725, 4.679, 0]}, {"s": [196.647, 109.702, 0], "t": 22.154}]}, "r": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [0], "t": 15}, {"s": [360], "t": 22.154}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "refId": "5", "ind": 5}, {"ty": 2, "nm": "left heart", "sr": 1, "st": 0, "op": 24, "ip": 0, "ln": "771", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [85.329, 77.035]}, "s": {"a": 1, "k": [{"s": [100, 100, 100], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 6}, {"s": [110, 110, 110], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 8.129}, {"s": [100, 100, 100], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 10.563}, {"s": [110, 110, 100], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 13}, {"s": [100, 100, 100], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 15}, {"s": [100, 100, 100], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 20.307}, {"s": [110, 110, 110], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 22.154}, {"s": [100, 100, 100], "i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 24}]}, "p": {"a": 0, "k": [197.315, 187.318, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "refId": "6", "ind": 6}, {"ty": 2, "nm": "right heart", "sr": 1, "st": 0, "op": 24, "ip": 0, "ln": "769", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [67.23, 62.496]}, "s": {"a": 1, "k": [{"s": [100, 100, 100], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 6}, {"s": [110, 110, 110], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 8.129}, {"s": [100, 100, 100], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 10.563}, {"s": [110, 110, 100], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 13}, {"s": [100, 100, 100], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 15}, {"s": [100, 100, 100], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 20.307}, {"s": [110, 110, 110], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 22.154}, {"s": [100, 100, 100], "i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 24}]}, "p": {"a": 0, "k": [324.753, 209.506, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "refId": "7", "ind": 7}, {"ty": 2, "nm": "Base", "sr": 1, "st": 0, "op": 24, "ip": 0, "ln": "767", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [173.652, 173.652]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [248.718, 250.412, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "refId": "8", "ind": 8}], "v": "5.7.0", "fr": 24, "op": 24, "ip": 0, "assets": [{"id": "1", "e": 0, "p": "/Users/<USER>/Desktop/Brand Jar/so true/Sotrue_all_1s/Sotrue_emojis_1s/(Footage)/love after/Love#03.wav", "u": ""}, {"id": "2", "e": 1, "w": 1, "h": 1, "p": "data:image/webp;base64,UklGRkgAAABXRUJQVlA4WAoAAAAQAAAAAwAAAwAAQUxQSAkAAAABBxAREYiI/gcAVlA4IBgAAAAwAQCdASoEAAQAAUAmJaQAA3AA/v02aAA=", "u": ""}, {"id": "3", "e": 1, "w": 136, "h": 70, "p": "data:image/webp;base64,UklGRiACAABXRUJQVlA4WAoAAAAQAAAAhwAARQAAQUxQSIMBAAABoBUASGXbIgIRiGAEIkwEI0wEIhjBCEYgghGMYAPOY54OnHPu/YqICYD/Qol/p+i4TjvtNVNYSe3uKCmkbI+OksLZ7PG+Uyg0nzOztgVS7eVZUhBkC/adIthXMLOW0T1dxMxaRt9s6ZbRL1rLzLqwU7zc7yr8IWJmJi9+73VnXIs3aWrnU8vmxOHUKpmZ6RXmLFWnPTkruXFXT5uIFD201wu69M1Ofwo28AjDMz2C+Gw70vj0SOIzPEh/AHwAIz45Kn8M9McA7Y+Bw8sn0KLjM4oOz6DENuAijtDKFeDQ6BKUwBRu9rjSHZpRFbjNQQ28BzkmhidLRALP1ngqPF2j6fgY1Fg6wosSSUd4NcfREV5OMwhFeJ16CBWWLAFkWHSbzo0Ey2JzrSGsvA235gaLozhVEdan5lBn+CarMyPDd1kdmYLwaapODEH4PO7jez2Dk1znl2ZN4OlW50daRnA3SV9ttozgNG5Fl1Fh8D5l0fGO1j1BnIlFqmq/oqpFMhP8nx0AVlA4IHYAAAAQBwCdASqIAEYAPm0wlEekIqIhK3EJYIANiWkC4AAABwzMlxvyzGFWf37k5GrHbEsP6H9DZrFmPlBPzRviAjtYAAD++eVU323kEEbnhv/oQ//pdAUoVZ78gvD/nQcFf0D25OY7N5Ouo35u5P//ju99if88AAAA", "u": ""}, {"id": "4", "e": 1, "w": 53, "h": 32, "p": "data:image/webp;base64,UklGRiYBAABXRUJQVlA4WAoAAAAQAAAANAAAHwAAQUxQSMAAAAABYEhtW50PIYQQhhBCCCH8CCGEEMKPEMIQhpDB7tt/EYgIRW7bNpEKJOk4jUfgk1jaGJUSLEm8v1kpGsUy9h/WEgyi7QJm1SZh3fddmCiHmC0rou8aRhTH2FVMEp/IyoQVR8ZEfciPXLS3hPomSwrURGKJDRbkPn/tsKGw4HgbYvtZvlqJUOe3gEvSYUdkH5G/msXSwB8mWEIfzybYflzvVQxTlrfZxxl8rHBxBh8JxuTnHwdrykO4uMGeNPYCHwBWUDggQAAAAPADAJ0BKjUAIAA+bSiRRaQiIZv5XgBABsS0gAAQWjhn9sDCxzGkzUAA/vnlA9kzf4PL+tc7/L509VQn/9XgAAA=", "u": ""}, {"id": "5", "e": 1, "w": 65, "h": 38, "p": "data:image/webp;base64,UklGRlIBAABXRUJQVlA4WAoAAAAQAAAAQAAAJQAAQUxQSOUAAAABgFZrb97mhSAIhmAIhmAIhlAIhhAIhhAIgmAIhiAGb7flUzr7HxETAJ+59k1VdWuCgKUrL+/NWR3G+6v5SX3x4VV9pME3Nb2XBl+2+pJ0Omyv1EWX9TnZ6VQfK0afqu2pzjdNR/+UkvCmDD6tWyvwKJOPai/wmibv26gCv9l4e69wnYw3rSf4lsnr1gXOZfKydYH7wcu7wP+HV1dBwMyruyCgrCsfhNx4bhkhM89nRkw9m4KYjadTEHSdTEHQxmNLiDqPLCNq5nFB2HHUENcOBuJW/lUE7n+mRDNBsJUROu1D8M8DAFZQOCBGAAAA8AMAnQEqQQAmAD5tLJJGpCIhoTMxyViADYlpAAAPv1b94MZcqRiiAAD++0n7vcv/QBYUN8n4sYRF5/ypTxVC39/p4AAAAA==", "u": ""}, {"id": "6", "e": 1, "w": 171, "h": 155, "p": "data:image/webp;base64,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", "u": ""}, {"id": "7", "e": 1, "w": 135, "h": 126, "p": "data:image/webp;base64,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", "u": ""}, {"id": "8", "e": 1, "w": 348, "h": 348, "p": "data:image/webp;base64,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", "u": ""}]}