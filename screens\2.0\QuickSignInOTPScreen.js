import {Keyboard, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import React, {useContext, useEffect, useState} from 'react';
import useSTheme from '../../theme/useSTheme';
import useSThemedStyles from '../../theme/useSThemedStyles';
import useDefaultStyle from '../../theme/useDefaultStyle';
import CustomStatusBar from '../../components/common/CustomStatusBar';
import LoginSignUpLinearGrad from '../../components/common/LoginSignUpLinearGrad';
import {ScrollView} from 'react-native';
import ModuleAppBar from '../../components/loginModule/ModuleAppBar';
import CustomProgressDialog from '../../components/common/CustomProgressDialog';
import EntutoTextView from '../../components/common/EntutoTextView';
import ModuleHeaderText from '../../components/loginModule/ModuleHeaderText';
import LoginModuleTitle from '../../components/loginModule/LoginModuleTitle';
import EntutoEditText from '../../components/common/EntutoEditText';
import PrimaryButton from '../../components/common/PrimaryButton';
import EntutoDropdown from '../../components/common/EntutoDropdown';
import SuccessFailureMsgBox from '../../components/common/SuccessFailureMsgBox';
import ServerConnector from '../../utils/ServerConnector';
import {checkValueLength} from '../../utils/Utils';
import {AppStateContext} from '../..';
import {CommonActions} from '@react-navigation/native';
import {_setAccessKey} from '../../utils/AuthLogin';
import {err} from 'react-native-svg/lib/typescript/xml';

const initialData = {
  data: {
    active_accs: ['<EMAIL>'], // Initially empty, will store email IDs if available
  },
  msg: 'test', // Message field, can be updated dynamically
};

const QuickSignInOTPScreen = ({route, navigation}) => {
  const {_data, _mobile} = route.params;
  const theme = useSTheme();
  const style = useSThemedStyles(styles);
  const {defaultStyle} = useDefaultStyle();
  const [showLoading, setShowLoading] = useState(false);
  const [errorMsg, setErrorMsg] = useState('');
  const [errorMsgType, setErrorMsgType] = useState('FAILED');
  const [refreshKey, setRefreshKey] = useState(Math.random());
  const [selectedEmailId, setSelectedEmailId] = useState('');
  const [showEmailField, setShowEmailField] = useState(false);
  const [emailIds, setEmailIds] = useState([]);
  const [minutes, setMinutes] = useState(1);
  const [seconds, setSeconds] = useState(30);
  const [disableResendOtpBtn, setDisableResendOtpBtn] = useState(true);
  const [otpVal, setOtpVal] = useState('');
  const [otpValErr, setOtpValErr] = useState('');
  const [selectedFirstEmail, setSelectedFirstEmail] = useState('');
  const {changeUserDetails} = useContext(AppStateContext);

  useEffect(() => {
    setShowEmailField(false);
    if (_data.hasOwnProperty('data')) {
      if (_data.data.active_accs.length == 1) {
        setSelectedFirstEmail(_data.data.active_accs[0]);
      }
      if (_data.data.active_accs.length > 1) {
        if (_data.hasOwnProperty('msg')) {
          setErrorMsg(_data.msg);
          setErrorMsgType('SUCCESS');
          setRefreshKey(Math.random());
        }
        setShowEmailField(true);
        let tempArray = [];
        _data.data.active_accs.map(item => {
          tempArray.push({
            label: item,
            value: item,
          });
        });
        setEmailIds(tempArray);
      }
    }
  }, [_data]);

  const VerifyBtnPress = () => {
    // navigation.replace("QuickSignUpScreen", {
    //     _mobile: _mobile,
    //     data: {}
    // })
    Keyboard.dismiss();
    if (formValid()) {
      setShowLoading(true);
      submitOTPService();
    }
  };
  const formValid = () => {
    let formValid = true;
    if (!checkValueLength(otpVal)) {
      setOtpValErr('Please enter OTP');
      formValid = false;
    }
    return formValid;
  };
  const onEmailIdOptionChange = item => {
    setSelectedEmailId(item.value);
  };
  const onOTPValueChange = text => {
    setOtpVal(text);
    setOtpValErr('');
  };
  useEffect(() => {
    let myInterval = setInterval(() => {
      if (seconds > 0) {
        setSeconds(seconds - 1);
      }
      if (seconds === 0) {
        if (minutes === 0) {
          clearInterval(myInterval);
        } else {
          setMinutes(minutes - 1);
          setSeconds(59);
        }
      }
      if (minutes == 0 && seconds == 0) {
        setDisableResendOtpBtn(false);
      }
    }, 1000);
    return () => {
      clearInterval(myInterval);
    };
  }, [seconds, minutes]);
  const resendOtpBtnPress = () => {
    resendOtpService();
    setShowLoading(true);
  };
  const resendOtpService = () => {
    let hashMap = {
      _action_code: '11:RESEND_OTP',
      mobile: _mobile,
      type: 'MOBILE',
    };
    let connector = new ServerConnector();
    connector.postData(
      hashMap,
      data => {
        // success method
        setShowLoading(false);
        // setErrorMsg(data.msg);
        // setErrorMsgType('SUCCESS');
        // setRefreshKey(Math.random());
        setDisableResendOtpBtn(true);
        setSeconds(30);
        setMinutes(1);
      },
      (errorCode, errorMessage, data) => {
        // failure method
        setShowLoading(false);
        var fieldErrorShown = false;
        if (errorCode === 'E006') {
          if (data && data != null && data.data) {
            if (data.data.mobile) {
              setMobileNumberErr(data.data.mobile);
              fieldErrorShown = true;
            }
          }
        }
        if (!fieldErrorShown) {
          if (errorCode == 'UE103') {
            setErrorMsg(errorMessage);
            setErrorMsgType('SUCCESS');
            setRefreshKey(Math.random());
            setDisableResendOtpBtn(true);
            setSeconds(30);
            setMinutes(1);
          } else {
            setErrorMsg(errorMessage);
            setErrorMsgType('FAILED');
            setRefreshKey(Math.random());
          }
        }
      },
    );
  };
  const submitOTPService = () => {
    let hashMap = {
      _action_code: '11:SUBMIT_OTP_NEW',
      mobile: _mobile,
      otp: otpVal,
    };
    if (checkValueLength(selectedEmailId)) {
      hashMap.email = selectedEmailId;
    }
    if (checkValueLength(selectedFirstEmail)) {
      hashMap.email = selectedFirstEmail;
    }
    let connector = new ServerConnector();
    connector.postData(
      hashMap,
      data => {
        // success method
        setShowLoading(false);
        setErrorMsgType('SUCCESS');
        setErrorMsg(data.msg);
        setRefreshKey(Math.random());
        if (data.hasOwnProperty('next_step')) {
          if (data.hasOwnProperty('data') && data.data.hasOwnProperty('uid')) {
            let userDetails = {
              _username: data.data.uid,
              _password: data.data.pwd,
              _profile_seq: data.data.profile_seq,
              _user_seq: data.data.user_seq,
              _user_handle: data.data.user_handle,
              _user_account_type: data.data.account_type,
              _user_display_name: '',
              _has_bank_details: 'NO',
              _is_profile_verified: 'NO',
              _is_gmail_login: 'YES',
              _max_file_size: data.data.max_file_size,
            };
            changeUserDetails(userDetails);
            if (data.data.hasOwnProperty('access_key')) {
              _setAccessKey(data.data.access_key);
            }
          }

          if (data.next_step == 'HOME') {
            goToHomeScreen();
          } else if (data.next_step == 'PERSONALIZE') {
            navigation.replace('QuickSignUpPersonalizeScreen');
          } else {
            navigation.replace('QuickSignUpScreen', {
              _mobile: _mobile,
              _selectedEmailID: selectedEmailId,
              data: data,
            });
          }
        } else {
          navigation.replace('QuickSignUpScreen', {
            _mobile: _mobile,
            _selectedEmailID: selectedEmailId,
            data: data,
          });
        }
      },
      (errorCode, errorMessage, data) => {
        // failure method
        setShowLoading(false);
        var fieldErrorShown = false;
        console.log('data.data', data, errorMessage, errorCode);
        if (errorCode === 'UE004') {
          // console.log('Err!!');
          setOtpValErr(errorMessage); // Show user-friendly message
          fieldErrorShown = true;
        }
        if (errorCode === 'E006') {
          if (data && data != null) {
            if (data.data && data.data.otp) {
              setOtpValErr(data.data.otp);
              fieldErrorShown = true;
            }
            if (data.data && data.data.msg) {
              setErrorMsg(data.data.msg);
              setErrorMsgType('FAILED');
              setRefreshKey(Math.random());
              fieldErrorShown = true;
            }
          }
        }
        if (!fieldErrorShown) {
          setErrorMsg(errorMessage);
          setErrorMsgType('FAILED');
          setRefreshKey(Math.random());
        }
      },
    );
    // setShowLoading(false);
    // setErrorMsgType('SUCCESS');
    // setErrorMsg(data.msg);
    // setRefreshKey(Math.random());

    // navigation.replace('QuickSignUpScreen', {
    //   _mobile: _mobile,
    //   _selectedEmailID: selectedEmailId,
    //   data: {},
    // });
  };
  const goToHomeScreen = () => {
    navigation.dispatch(
      CommonActions.reset({
        index: 1,
        routes: [
          {
            name: 'VideoContentScreen',
            params: {
              postSeq: -1,
              postProfileSeq: -1,
              cameFrom: 'MOBILE_OTP',
            },
          },
        ],
      }),
    );
    //Comment out on 14/11/2024
    // navigation.dispatch(
    //     CommonActions.reset({
    //         index: 1,
    //         routes: [
    //             {
    //                 name: 'HomeScreen',
    //             },
    //         ],
    //     })
    // );
  };
  return (
    <>
      <CustomStatusBar translucent={false} hidden={false} />
      <CustomProgressDialog showLoading={showLoading} />
      <View style={{flex: 1, position: 'relative'}}>
        <LoginSignUpLinearGrad />
        <ModuleAppBar navigation={navigation} />
        <ScrollView keyboardShouldPersistTaps="handled">
          <View style={defaultStyle.loginModuleContainer}>
            {/* <ModuleHeaderText
              text="Welcome to Sotrue"
              style={{marginTop: theme.dimensions.loginModuleHeaderTextMT}}
              // secondText="Exclusive Content, Tailored Just For You"
            /> */}
            <LoginModuleTitle
              firstTitleText="Quickly Give Us Your Digits!"
              // secondTitleText="Use Your Digits"
              style={{marginTop: 50}}
            />
            <View style={defaultStyle.loginModuleFormContainer}>
              <View>
                <EntutoEditText
                  label="Mobile Number"
                  placeholderTxt="Mobile Number"
                  value={_mobile}
                  disabledField={true}
                  type="number-pad"
                  leftComponent={
                    <Text
                      style={{
                        fontSize: 16,
                        color: theme.colors.inputTextColor,
                        marginRight: 6,
                        alignSelf: 'center',
                      }}>
                      +91
                    </Text>
                  }
                  showLeftIcon={true}
                />
              </View>
              {showEmailField ? (
                <View style={{marginTop: theme.dimensions.loginModuleInputMT}}>
                  <EntutoDropdown
                    label="Email ID"
                    placeholder="Select Email ID"
                    value={selectedEmailId}
                    options={emailIds}
                    onOptionChange={onEmailIdOptionChange}
                  />
                </View>
              ) : null}
              <View style={{marginTop: theme.dimensions.loginModuleInputMT}}>
                <EntutoEditText
                  label="OTP"
                  placeholderTxt="OTP"
                  keyboardType="numeric"
                  value={otpVal}
                  showErrorField={otpValErr.length}
                  returnKeyType="go"
                  onSubmitEditing={() => VerifyBtnPress()}
                  errorMsg={otpValErr}
                  onChangeText={onOTPValueChange}
                />
              </View>
              <View style={{flexDirection: 'row', marginTop: 8}}>
                <EntutoTextView>Resend OTP in</EntutoTextView>

                <EntutoTextView
                  style={{color: theme.colors.primaryColor, marginStart: 4}}>
                  {minutes}:{seconds < 10 ? `0${seconds}` : seconds}
                </EntutoTextView>

                <TouchableOpacity
                  disabled={disableResendOtpBtn}
                  onPress={() => resendOtpBtnPress()}>
                  <EntutoTextView
                    style={{
                      color: theme.colors.primaryColor,
                      marginStart: 8,
                      opacity: disableResendOtpBtn ? 0.3 : 1,
                    }}>
                    Resend OTP
                  </EntutoTextView>
                </TouchableOpacity>
              </View>
              <View style={{marginTop: theme.dimensions.loginModuleButtonMT}}>
                <PrimaryButton
                  label="Verify"
                  style={{}}
                  onPress={() => VerifyBtnPress()}
                />
              </View>
            </View>
          </View>
        </ScrollView>
      </View>
      {/* {errorMsg.length != 0 ? (
        <SuccessFailureMsgBox
          alertMsg={errorMsg}
          alertKey={refreshKey}
          alertType={errorMsgType}
        />
      ) : null} */}
    </>
  );
};

export default QuickSignInOTPScreen;

const styles = theme => StyleSheet.create({});
