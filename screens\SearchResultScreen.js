import {
  FlatList,
  Image,
  Keyboard,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {
  useCallback,
  useContext,
  useEffect,
  useRef,
  useState,
} from 'react';
import useSTheme from '../theme/useSTheme';
import useSThemedStyles from '../theme/useSThemedStyles';
import {AppStateContext} from '..';
import CustomStatusBar from '../components/common/CustomStatusBar';
import useDefaultStyle from '../theme/useDefaultStyle';
import CustomTabButtonGroup from '../components/common/CustomTabButtonGroup';
import EntutoTextView from '../components/common/EntutoTextView';
import TopicsSearchComponent from '../components/search/TopicsSearchComponent';
import ProfileSearchComponent from '../components/search/ProfileSearchComponent';
import PlayListSearchComponent from '../components/search/PlayListSearchComponent';
import {KEYWARDS_ARRAY_SIZE} from '../utils/Appconfig';
import TempData from '../data/TempData';
import useOrientationManager from '../hooks/useOrientationManager';

const SearchResultScreen = ({navigation}) => {
  // Lock orientation to portrait for SearchResultScreen
  useOrientationManager(false);

  const theme = useSTheme();
  const searchInputRef = useRef(null);
  const style = useSThemedStyles(styles);
  const {defaultStyle} = useDefaultStyle();
  const {searchKeywardHistory, changeKeywardHistory} =
    useContext(AppStateContext);

  const [searchQuery, setSearchQuery] = useState('');
  const [isTyped, setIsTyped] = useState(false);
  const [showOptions, setShowOptions] = useState(false);
  const TAB_LIST = [
    {label: 'Topics', value: 'TOPICS'},
    {label: 'Playlist', value: 'PLAYLIST'},
    {label: 'Profiles', value: 'PROFILES'},
  ];
  const [selectedTab, setSelectedTab] = useState('TOPICS');
  const [searchedKeys, setSearchedKeys] = useState([]);

  useEffect(() => {
    assignSearchKeys();
  }, [searchKeywardHistory]);
  const assignSearchKeys = () => {
    let tempList = [];
    searchKeywardHistory.map((item, i) => {
      tempList.push(item);
    });
    setSearchedKeys([...[], ...tempList]);
  };

  const onChangeSearch = useCallback(
    query => {
      if (query.length != 0) {
        setShowOptions(false);
        setIsTyped(true);
      } else {
        setShowOptions(true);
        setIsTyped(false);
      }
      setSearchQuery(query);
    },
    [isTyped],
  );
  const cancelBtnClick = () => {
    setShowOptions(false);
    setSearchQuery('');
    setIsTyped(false);
    Keyboard.dismiss();
  };
  const clearSearchTxt = () => {
    setSearchQuery('');
    setShowOptions(false);
    setIsTyped(false);
    Keyboard.dismiss();
  };

  const searchInputFocus = useCallback(() => {
    setShowOptions(true);
  }, []);
  const searchInputBlur = useCallback(() => {
    setShowOptions(false);
  }, []);
  const onTabValueChange = useCallback(tabValue => {
    Keyboard.dismiss();
    setSelectedTab(prevSTate => (prevSTate = tabValue));
  }, []);
  // Searched Keys
  const historyItemBtnPress = (clickID, index) => {
    if (clickID == 'DELETE') {
      let tempArray = JSON.parse(JSON.stringify(searchedKeys));
      if (index > -1) {
        tempArray.splice(index, 1);
      }
      setSearchedKeys([...[], ...tempArray]);
      changeKeywardHistory(tempArray);
    }
    if (clickID == 'INSERT') {
      const item = searchedKeys[index];
      setShowOptions(false);
      setSearchQuery(item);
      setIsTyped(true);
    }
  };
  const renderKeywordHistoryItem = useCallback(
    ({item, index}) => {
      return (
        <HistorySearchItemRow
          index={index}
          value={item}
          historyItemBtnPress={historyItemBtnPress}
        />
      );
    },
    [searchedKeys],
  );
  const ItemHistorySeparatorComponent = () => {
    return <View style={{height: 2}} />;
  };
  const backBtnPress = () => {
    if (navigation.canGoBack()) {
      navigation.goBack(null);
    } else {
      navigation.replace('HomeScreen');
    }
  };
  const itemCallback = (clickID, obj) => {
    if (clickID == 'STORE_QUERY') {
      storeQueryForHistory(obj.query);
    }
    if (clickID == 'STORE_BACKUP_PROFILE') {
      const queryValue = obj.query;
      const searchData = obj.data;
      let tempArray = [...TempData.profilesBackupData];
      let queryValueNotFound = true;
      tempArray.map(item => {
        if (queryValue == item.query) {
          queryValueNotFound = false;
          item.searchData = searchData;
        }
      });
      if (queryValueNotFound) {
        tempArray.push({
          query: queryValue,
          searchData: searchData,
        });
      }
      TempData.profilesBackupData = tempArray;
    }
    if (clickID == 'STORE_BACKUP_PLAYLIST') {
      const queryValue = obj.query;
      const searchData = obj.data;
      let tempArray = [...TempData.playlistBackupData];
      let queryValueNotFound = true;
      tempArray.map(item => {
        if (queryValue == item.query) {
          queryValueNotFound = false;
          item.searchData = searchData;
        }
      });
      if (queryValueNotFound) {
        tempArray.push({
          query: queryValue,
          searchData: searchData,
        });
      }
      TempData.playlistBackupData = tempArray;
    }
  };
  const storeQueryForHistory = queryValue => {
    let historyKeys = searchKeywardHistory;
    if (!historyKeys.includes(queryValue)) {
      if (historyKeys.length == KEYWARDS_ARRAY_SIZE) {
        historyKeys.pop();
      }
      historyKeys.unshift(queryValue);
      changeKeywardHistory(historyKeys);
    }
  };

  return (
    <>
      <CustomStatusBar translucent={false} hidden={false} />
      <View
        style={{
          ...defaultStyle.container,
          backgroundColor: theme.colors.backgroundColor,
          paddingHorizontal: 4,
        }}>
        <View>
          <TouchableOpacity
            onPress={() => backBtnPress()}
            style={{marginRight: 20}}>
            <View style={{marginBottom: 10, marginTop: 10}}>
              <Image
                source={require('../assets/Images/icon/Arrow.png')}
                style={style.searchIcon}
                resizeMode="contain"
              />
            </View>
          </TouchableOpacity>
        </View>
        <View style={style.searchBarBox}>
          <View style={style.searchBar}>
            <Image
              source={require('../assets/Images/icon/search_icon.png')}
              style={style.searchIcon}
            />
            <TextInput
              ref={searchInputRef}
              numberOfLines={1}
              style={style.input}
              placeholder="Search"
              placeholderTextColor={theme.colors.inputPlaceholderColor}
              value={searchQuery}
              autoCorrect={false}
              autoFocus={false}
              onFocus={searchInputFocus}
              onBlur={searchInputBlur}
              onChangeText={onChangeSearch}
              selectionColor={theme.colors.primaryColor}
            />
            {isTyped && (
              <View>
                <TouchableOpacity onPress={() => clearSearchTxt()}>
                  <View style={{paddingEnd: 10}}>
                    <Image
                      source={require('../assets/Images/icon/c_remove.png')}
                      style={style.searchCrossIcon}
                    />
                  </View>
                </TouchableOpacity>
              </View>
            )}
          </View>
        </View>
      </View>
      <View
        style={{
          ...defaultStyle.container,
          backgroundColor: theme.colors.backgroundColor,
          paddingHorizontal: 0,
          position: 'relative',
          flex: 1,
        }}>
        {showOptions ? (
          <View style={style.searchHistoryBox}>
            <FlatList
              keyboardShouldPersistTaps={'handled'}
              removeClippedSubviews={true}
              renderItem={renderKeywordHistoryItem}
              data={searchedKeys}
              keyExtractor={(item, index) => `${index}`}
              ItemSeparatorComponent={ItemHistorySeparatorComponent}
              maxToRenderPerBatch={1000}
              windowSize={60}
              updateCellsBatchingPeriod={50}
              initialNumToRender={50}
              disableVirtualization
              onEndReachedThreshold={40}
              showsVerticalScrollIndicator={false}
              showsHorizontalScrollIndicator={false}
            />
          </View>
        ) : null}
        <CustomTabButtonGroup
          selectedTab={selectedTab}
          tabList={TAB_LIST}
          boxStyle={{
            paddingHorizontal: 1,
            marginBottom: 10,
          }}
          onTabValueChange={onTabValueChange}
        />
        <View style={{flex: 1}}>
          {selectedTab == 'TOPICS' ? (
            <TopicsSearchComponent
              searchText={searchQuery}
              navigation={navigation}
              itemCallback={itemCallback}
            />
          ) : null}
          {selectedTab == 'PLAYLIST' ? (
            <PlayListSearchComponent
              searchText={searchQuery}
              navigation={navigation}
            />
          ) : null}
          {selectedTab == 'PROFILES' ? (
            <ProfileSearchComponent
              searchText={searchQuery}
              navigation={navigation}
              itemCallback={itemCallback}
            />
          ) : null}
        </View>
      </View>
    </>
  );
};

export default React.memo(SearchResultScreen);
const HistorySearchItemRow = ({historyItemBtnPress, value, index}) => {
  const style = useSThemedStyles(styles);
  const theme = useSTheme();
  const deleteHistoryData = () => {
    // console.log("Press")
    historyItemBtnPress('DELETE', index);
  };
  const itemBtnPress = () => {
    historyItemBtnPress('INSERT', index);
  };
  return (
    <View style={style.historySearchItem}>
      <TouchableOpacity style={{flex: 1}} onPress={() => itemBtnPress()}>
        <EntutoTextView style={style.historySearchItemText}>
          {value}
        </EntutoTextView>
      </TouchableOpacity>
      <View style={{zIndex: 2}}>
        <TouchableOpacity
          style={{paddingHorizontal: 8}}
          onPress={() => deleteHistoryData()}>
          <View>
            <Image
              source={require('../assets/Images/icon/close_icon.png')}
              style={style.historySearchItemCloseIcon}
            />
          </View>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = theme =>
  StyleSheet.create({
    searchBarBox: {
      alignItems: 'center',
      flexDirection: 'row',
      marginTop: 8,
      position: 'relative',
    },
    searchBar: {
      flex: 1,
      flexDirection: 'row',
      borderWidth: 1,
      borderColor: '#C1C0C8',
      alignItems: 'center',
      minHeight: 40,
      paddingStart: 16,
    },
    searchIcon: {
      height: theme.dimensions.searchInputIconH,
      width: theme.dimensions.searchInputIconH,
      tintColor: '#CCC',
    },
    searchCrossIcon: {
      height: theme.dimensions.searchInputIconH,
      width: theme.dimensions.searchInputIconH,
      tintColor: '#CCC',
      paddingEnd: 8,
    },
    input: {
      fontSize: theme.calculateFontSize(theme.dimensions.searchResultInputText),
      marginHorizontal: 8,
      padding: 0,
      flex: 1,
      color: theme.colors.inputTextColor,
      minHeight: 40,
    },
    searchHistoryBox: {
      position: 'absolute',
      top: -14,
      left: 0,
      right: 0,
      maxHeight: 200,
      zIndex: 100,
      marginHorizontal: 4,
      backgroundColor: theme.colors.backgroundColor,
      borderWidth: 1,
      borderColor: '#C1C0C8',
    },
    historySearchItem: {
      paddingVertical: 4,
      flexDirection: 'row',
      borderBottomColor: '#C1C0C8',
      borderBottomWidth: 0.5,
      alignItems: 'center',
    },
    historySearchItemText: {
      color: theme.colors.primaryTextColor,
      fontSize: theme.calculateFontSize(
        theme.dimensions.searchResultHistoryItemText,
      ),
      paddingStart: 10,
      paddingEnd: 15,
    },
    historySearchItemCloseIcon: {
      height: 20,
      width: 20,
      resizeMode: 'contain',
    },
  });
