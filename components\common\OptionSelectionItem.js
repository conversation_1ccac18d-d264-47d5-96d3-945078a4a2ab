import {
  Image,
  StyleSheet,
  TouchableOpacity,
  View,
  Vibration,
  Animated,
} from 'react-native';
import React, {useRef, useState, useEffect} from 'react';
import useSThemedStyles from '../../theme/useSThemedStyles';
import useSTheme from '../../theme/useSTheme';
import EntutoTextView from './EntutoTextView';
import LottieView from 'lottie-react-native';
import Sound from 'react-native-sound';

const OptionSelectionItem = ({
  isSearchIcon = false,
  onItemSelected = null,
  index = 0,
  label = '',
  value = '',
  isChecked = false,
  showImage = false,
  inactiveImageValue = null,
  activeImageValue = null,
  isAnimated = false,
  lottieFile = null,
  soundFileName = null,
  vibrationPattern = null,
}) => {
  const style = useSThemedStyles(styles);
  const theme = useSTheme();
  const lottieRef = useRef(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [sound, setSound] = useState(null);
  const scaleAnim = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    if (soundFileName) {
      const soundObject = new Sound(soundFileName, Sound.MAIN_BUNDLE, error => {
        if (error) {
          return;
        }
        setSound(soundObject);
      });
    }
    return () => {
      if (sound) {
        sound.release();
      }
    };
  }, [soundFileName]);

  const performScaleAnimation = () => {
    if (isChecked) return;
    Animated.sequence([
      Animated.timing(scaleAnim, {
        toValue: 2,
        duration: 500,
        useNativeDriver: true,
      }),
      Animated.delay(500),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 500,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const searchBtnClick = () => {
    if (onItemSelected) {
      onItemSelected('SEARCH', {});
    }
  };

  const onOptionItemClick = () => {
    if (!isChecked) {
      if (isAnimated && lottieRef.current) {
        lottieRef.current.reset();
        lottieRef.current.play();
        setIsPlaying(true);
      }

      if (isAnimated && showImage) {
        performScaleAnimation();
      }

      isAnimated && vibrationPattern
        ? Vibration.vibrate(vibrationPattern)
        : Vibration.vibrate(100);

      // if (isAnimated && sound) {
      //   sound.play(success => {
      //     if (!success) {
      //       // fallback can be added here if needed
      //     }
      //   });
      // }
    }

    if (onItemSelected) {
      onItemSelected('ITEM_CLICK', {index, value, isChecked});
    }
  };

  const onAnimationFinish = () => {
    setIsPlaying(false);
  };

  const renderIcon = () => {
    if (isAnimated && lottieFile) {
      return (
        <Animated.View style={{transform: [{scale: scaleAnim}]}}>
          <LottieView
            ref={lottieRef}
            source={lottieFile}
            style={style.listItemIcon}
            loop={false}
            autoPlay={false}
            onAnimationFinish={onAnimationFinish}
          />
        </Animated.View>
      );
    } else {
      return (
        <Image
          source={isChecked ? activeImageValue : inactiveImageValue}
          style={style.listItemIcon}
        />
      );
    }
  };

  return (
    <>
      {isSearchIcon ? (
        <View>
          <TouchableOpacity onPress={() => searchBtnClick()}>
            <View
              style={[
                style.itemContainer,
                {
                  borderColor: theme.colors.optionSelectionItemColor,
                  minWidth: 90,
                  width: isAnimated ? '50%' : 24,
                  height: isAnimated ? 42 : 24,
                },
              ]}>
              <Image
                source={isChecked ? activeImageValue : inactiveImageValue}
                style={style.searchIcon}
              />
            </View>
          </TouchableOpacity>
        </View>
      ) : (
        <View>
          <TouchableOpacity onPress={() => onOptionItemClick()}>
            <View
              style={[
                style.itemContainer,
                {
                  borderColor: isChecked
                    ? theme.colors.primaryColor
                    : theme.colors.optionSelectionItemColor,
                  flexDirection: 'row',
                  padding: 0,
                },
              ]}>
              {showImage ? renderIcon() : null}
              {label ? (
                <EntutoTextView style={style.itemContainerText}>
                  {label}
                </EntutoTextView>
              ) : null}
            </View>
          </TouchableOpacity>
        </View>
      )}
    </>
  );
};

export default OptionSelectionItem;

const styles = theme =>
  StyleSheet.create({
    itemContainer: {
      borderWidth: 1,
      justifyContent: 'center',
      alignItems: 'center',
      minHeight: 36,
      marginEnd: 8,
      marginBottom: 8,
      minWidth: 90,
      paddingHorizontal: 6,
      paddingVertical: 3,
    },
    itemContainerText: {},
    searchIcon: {
      width: 24,
      height: 24,
      resizeMode: 'contain',
      tintColor: theme.colors.optionSelectionItemColor,
      paddingHorizontal: 8,
    },
    listItemIcon: {
      width: 34,
      height: 34,
      resizeMode: 'contain',
      marginEnd: 3,
    },
  });
