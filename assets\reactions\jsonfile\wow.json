{"nm": "Wow!", "h": 269, "w": 296, "meta": {"g": "@lottiefiles/toolkit-js 0.65.0", "tc": "#ffffff"}, "layers": [{"ty": 2, "nm": "text", "sr": 1, "st": 0, "op": 24, "ip": 14, "ln": "653", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [232, 186.5]}, "s": {"a": 1, "k": [{"s": [100, 100, 100], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 9.714}, {"s": [113, 113, 0], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 12}, {"s": [113, 113, 100], "i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 14.286}, {"s": [100, 100, 88.496], "i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 16}]}, "p": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [126, 109.577, 0], "t": 6.285, "ti": [0, 0, 0], "to": [26.75, 12.423, 0]}, {"o": {"x": 0.333, "y": 0.333}, "i": {"x": 0.667, "y": 0.667}, "s": [163.038, 127.296, 0], "t": 9.714}, {"o": {"x": 0.333, "y": 0.333}, "i": {"x": 0.667, "y": 0.667}, "s": [163.038, 127.296, 0], "t": 12}, {"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [163.038, 127.296, 0], "t": 14.286}, {"s": [155, 142.577, 0], "t": 16, "ti": [-26.75, -12.423, 0], "to": [0, 0, 0]}]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "refId": "1", "ind": 1}, {"ty": 2, "nm": "backgrounf", "sr": 1, "st": 0, "op": 24, "ip": 0, "ln": "651", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [21.518, 203.565]}, "s": {"a": 1, "k": [{"s": [9, 24, 100], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 0}, {"s": [111, 111, 99.107], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 9.714}, {"s": [111, 111, 100], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 12}, {"s": [111, 111, 100], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 14.286}, {"s": [100, 100, 90.09], "i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 16}]}, "p": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [23.5, 254.577, 0], "t": 0, "ti": [0, 0, 0], "to": [32.75, -4.077, 0]}, {"o": {"x": 0.333, "y": 0.333}, "i": {"x": 0.667, "y": 0.667}, "s": [70.5, 236.577, 0], "t": 9.714}, {"o": {"x": 0.333, "y": 0.333}, "i": {"x": 0.667, "y": 0.667}, "s": [70.5, 236.577, 0], "t": 12}, {"s": [70.5, 236.577, 0], "t": 14.286, "ti": [-32.75, 4.077, 0], "to": [0, 0, 0]}]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "refId": "2", "ind": 2}, {"ty": 2, "nm": "Layer 10", "sr": 1, "st": 0, "op": 24, "ip": 16, "ln": "661", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [8, 6]}, "s": {"a": 1, "k": [{"s": [100, 100, 100], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 16}, {"s": [115, 115, 98.291], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 21}, {"s": [100, 100, 100], "i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 24}]}, "p": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [69.5, 157.327, 0], "t": 16}, {"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [15, 138.327, 0], "t": 21}, {"s": [31, 144.077, 0], "t": 24}]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "refId": "3", "ind": 3}, {"ty": 2, "nm": "Layer 9", "sr": 1, "st": 0, "op": 24, "ip": 16, "ln": "659", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [12, 12]}, "s": {"a": 1, "k": [{"s": [100, 100, 100], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 16}, {"s": [115, 115, 98.291], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 21}, {"s": [100, 100, 100], "i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 24}]}, "p": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [66.25, 151.327, 0], "t": 16}, {"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [15.5, 126.327, 0], "t": 21}, {"s": [27, 132.077, 0], "t": 24}]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "refId": "4", "ind": 4}, {"ty": 2, "nm": "Layer 8", "sr": 1, "st": 0, "op": 24, "ip": 16, "ln": "657", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [7.5, 5.5]}, "s": {"a": 1, "k": [{"s": [100, 100, 100], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 16}, {"s": [115, 115, 98.291], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 21}, {"s": [100, 100, 100], "i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 24}]}, "p": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [226.75, 139.577, 0], "t": 16}, {"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [272, 125.077, 0], "t": 21}, {"s": [254.5, 130.577, 0], "t": 24}]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "refId": "5", "ind": 5}, {"ty": 2, "nm": "Layer 7", "sr": 1, "st": 0, "op": 24, "ip": 16, "ln": "655", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [10.5, 11.5]}, "s": {"a": 1, "k": [{"s": [100, 100, 100], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 16}, {"s": [115, 115, 98.291], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 21}, {"s": [100, 100, 100], "i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 24}]}, "p": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [229.5, 134.077, 0], "t": 16}, {"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [273.75, 110.827, 0], "t": 21}, {"s": [259.5, 118.577, 0], "t": 24}]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "refId": "6", "ind": 6}, {"ty": 6, "nm": "wow#01.wav", "sr": 1, "st": -75, "op": 35, "ip": 6, "ln": "1039", "au": {"lv": {"a": 0, "k": [0, 0]}}, "refId": "7", "ind": 7}], "v": "5.7.0", "fr": 24, "op": 24, "ip": 0, "assets": [{"id": "1", "e": 1, "w": 464, "h": 373, "p": "data:image/webp;base64,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", "u": ""}, {"id": "2", "e": 1, "w": 203, "h": 205, "p": "data:image/webp;base64,UklGRuQNAABXRUJQVlA4WAoAAAAQAAAAygAAzAAAQUxQSN0DAAABoFbbTl3hioRIQAISkFAJSKgEJCABCZUQCUhAQhzku6+e8trcnxExAbRbH8KV/l1ESvr3O4TgCeZwpSzVejeRlIIHxsckzQZvki6Hhruy2LwqKaDgYmm2QLn97vgqzdbZst+Xux9bbstuR+6utmiJm3F3tZW3yPuIjy1f0x58Udtii+uLYvsUvzROantN63LF9lv9mlyxLWtcUBDbdl6Ne2znD6/EFdt85WVwsv1XXkRUQ7DyCkI1EJ/5OBuOZbZLDck4FT+GpfqJLjU06zT8GKBpktAMUjdFMlBlAhaDNQzn1XCV0aJBG8Yqhu0zEouh68Zx1eDNw3g1fNsoXg1hP4ZXgzgPEQ3kNkI0mF2/YDjHbl6BKr28GtC1k1ODug9Xwzp0EQM79iiGduoQDe7nO294y2fcAKufPYb4V9HOwelBiJ3DbaC7L1hRC18UO4dgByG48W/RcKff20FEO4h2ENGAbz9V5OSXYAdRoMs/sEGffrixiz9U7MI7Z9jzuxs8el+xk3fOsH/eRfDSuwLe9a6B5145w17pdQRP3mXw0rsK3vXOwHevAnhKr2/wnncFvPudgBfeGfj02oEn7wJ46V0CL7zL2Cm9F+yeHyp29w+GvTuIRu8DdvkkroNQ+jFBV07iOgilgygncR1Eo4PIJ+EOQugg4kEoHUT64oKNvwioFToI9wmBVuhbxcx9JJAV+jhD5r66EUv0tQdM+TMCLNL3D1xCHW+4XA+PVqKuDSuhvhkqdZ0cVBf1rkBl6h5xEhpQUao8QgJJmUZkhUg9jXkjpJ5Gbfiop2EDPNXTwBmcyjQyN2gK09gemZuGj7A0TxMWUB6mKQsietGkXPF4mKZlAaMFmrogoYlmzzBoYpo/KgSamJbo6/40MS0zba5GWqqXfWnxtNzY9vREphVz0u08kWnZnNpGtESmxUfZgyRPW3S5LU5SoI3WddUSPe31thU3SdHTflnXolLSFWjXxZaoIjnF4GnvwaYVEckppRACodiG0ys4AjXZ8DfB6nQ8i7A8NmMA5bIpKybc5jAHSbJJAyLODkKmYUCCzSoEaJlFPSIyiV50DOII0jKDXgRqmCAzwSqjtUDAch0rEbipjSOOAPZ5CL0JYtYRHiaMxfq3QCDf1j8zgey1W/UEc7XOehPO2TqLI5yD9dWLgObWpzAh/VjPFgjqaD0TYe20g3gCW+xzvQntZJ8/TGh7+7pdBDfXrzIT3tm+rZ4Av+xTTYQ46yfiCPLHPtSLML/tw8KEOetvLRDq0X5OTLCnX8QT8D/oTdD7V48j8OW/2kXwc/23zHSAnKrpE+j/fABWUDgg4AkAANAzAJ0BKssAzQA+bTaXSKQjIiEiVVoYgA2JTd+Pkvt7gU3/mdbP61+S35KdbvtJ39/HbqlKY+gD4YvHf1//X/2f8jO0h+kvYA/Tb/J9TDzAfrp/1f8z72Pox/wnqAf1f+6dZN6BP7demb+xnwj/2T/eftn7R//pzoTtC5JRBAxVciNrv/AbyJlL0UZr6qeaN5GPrT2EP109Ln2AftP7In7bjg48zAT+VZ0jjp18hFjr8Y08VE+b4A+Z7/MyYSbFkrWeI4JUYkj1th+i9YyvcnpRbZ6+YCI65/Jdjo2vAT6H7iFj8crjfOLh3gklB8Of/0312Q+Z8nV2KUWDgZ3a7XuTpqgR3u2DyiLFnv0gewznD/ZDC6MhMTDTEtz60DjxOTBxzn40y6RBMKOdSU8Nz1e65O3ZPj12PcOKgzqJKEqS6+Brra4HwjCD9hf//7y03rWwvMKCTZuqXjuqa8/HCmgunjTToF9C06KorWTM16AAsmJs5tp3dOL/dJcfkuzmwpcbm+ef7Kf7Ib3PJHND1AJURz3vOt5DU6XwpQNH/3NZIAK9YSxYAAD+/snAUm1IKgVphTwwJ/00r3FlcBpA3dVBuzLpmsczh8YUsNmykHG/Q3dl5/jkEB6KXvJYxj2ybwVEe8hQ9EyWYlSAPKM7F4xC0osZEX8tSjbyYoV8tYNF9+nourdAvQ1wd4VpE5d3pv1SGWR0SuDzfEqbM2xDF9IH9/jlx+xHUlfa5EVRt0ZC3z6mLUE6XC7DxhzQ5IOoSrbvQCkwmGqhgMreGh+NrAXtBQAZf6BZGMuFA/ihlc9RgIUiXiIAOrKTv6mBv4LxE42b+S96yhrG2oH6YWmyBSA5hLT3EeA56gL+XsrR0Ne69gBr3mivJt7RbALwg6WNpoZjOAe+dM/+kHFIQewNm982lNARrqSw5a/JBIfH4pvlcY/EfUS4pv0b4BrxzNH63jMWVDYVXVNOLzTQJyZH6DGERZZY+csnjnae6dL0/ub6L/CoewZDy2ceP+BsYFsUqIgQoeALvoWwNvqwT2dgzOiYeeQeHMx+xqpMh9OO7OxMaCeGGC8JYFayrj1m5JCwuMD/+SUTzajv4G2XK4nKjit872L5KUbq8Z1wBsZm+LE54LjnNvK5YrIW1ljZAM7MyuPt9+GWflg/5zHuviWIQcB1i4EjRtqAh9ApIu6eecyzkL2EI3F0aFyf7VIsF64r5ZsGCozTBWTW4JNHFqbhwOR5AfTUPQawPo0wA59FXkeH4wa1IOgyi9mY8E2zR95cUYlW+eEYAnazVlkx3JOwCOFm6dfhCJN176FeI5W5eBO+riNc4NVh8NDQfFi5oakhBQ5MWXwhOhwAFA2rhzkoYz/HUwX/eplHMs48rVaOkfbliWbtZMT3ZBYKWxYQKbQBwvipztw8qfBKlvE/s7u/dj1/Hb8LonMoEljd6m8gmH4bYNyo1R5FAP9jYIId4QSgmgN3428kOlbQ4mItADo9iiV2wElL4pC9IlMivoYnbcN7wrSKx331UiA0I7OQed3eGuVBajg0pRDo5BefupYAcfgaW/MBYbk29taag6z5eXl3TnIFTcbHKib+aBxZZ4M5MR9hglXX/epVYUoZhN+opfGawAgfPHoV4rZVnosILgxTGmAszbuZvhvMAf5kryvAnsLsoOa1/8MXJRTnCI4+iOuk8Z+lIaZHadbR/vaJx9vWOFqgzAHI023ZsEK514ICMMarsp/zHk+0jseZ3U2rDDHFwZRm6bbjw7cCPEyFiTV9t220TifdDNzc54APqzllgydRzFDi7kpEshKkpcCpIa6kqfj7YYrN1hoG49JLybzbfOFhLacHK6UCy5Tx9daX3iLNRE758iONKnR7cnvcHm4GZndvewgC9Y0IzGpoSL+huQqMhy32YkLY/AKFWoTv3R51LXHbOPTFN006kIk1rajUZgMc1uAyahRvN3yLbzKyvRA1EBBQ/M/gij14HtrvWosEsZm3lEzC9VRQ3TdHf7JX3K0Efs/ezJTd6AwBtgq3nSh7r6WTf1LIfW7RBNEL4RbfiCxnw9ZtVioWH9YrHmu3UHxxgH9uGiMyi4Yc8oijzdWhLf3xrLpzQMC/05PhlrvQbMCMLJ13QwXKCAG9yFebsd3rz84dOwG0aOAtTxiIjjk/H7YxRxzxK2elmMFXnEodw9f/1QdhDqZ6a17U5uf3wDLozrcifJfaWXXbSBNZDcJVgV0OsbwGJX/w4K5/y/qpR29Xdvx+lpqpqdzJ1t2vSpYUrVogzNGgupJBcc8+MIbF7fe8XbY+QCFCvoRf3mfmsGFeFi5EvhvePHpF2Igp+CxYHvmjyWsHwZqGSdb6lIXLr1JsrxThDH39Sl0M1yYw3ZqbelSuAkyGn3j16rNU33mu4td67sY7jNrE0HoMk042yoh6QyvUUIJni0cbOwfaRLXDcEKvQ2LNfHbcOpW8ea+j8/YTxsfsBs5IR7GbyowE4S11HZGGUGeNYuMDCY2WGlF3NaiA5Hupsmy3aXQ9hHQtX1HEWXVCZ6ExOPLHZBFdq/gEwvmC+5c6711UgxgLDO2Oua4/IelD/yRNpwPNLuDE+9b40M52oDEZWlDDjwLT247hs/7h+nOoZsGRsXeI7cQ1+1gK0oS/uUQf+v6T0vU9auU/Am3odbK8Aq4DxOlZyhbpvvjglv3sjXFg2/HcndnnahsgraFDjzGTzLVqpCqe7SQCAfLYsBasXiw/MkOnyvRniVDYNDwNh60F6AxXxEiayG0QRiZF4ZzFCYZpf+mRcS1qVei8TCGjI2CWrRRIxgMrgukUAdnkQBfesxmnqM03JP0FfnLSI7lTL+OcBklyopjd925Q3PRxHDDapDUeE/6K3KnQ105xFZmFENYejhCj8erZLT/2cIY0fOE8vmCVtjOOL1ZtIANp0qu0iW7+l66nX2gi1WoZ/K6raWI0Gt9QgkyopEtcpp3+Aa0l7in+fPvuuo3P3nwXoSXu/M/UyoJHuJtf1b20nIffNNnxKv/3cegAQW1m077LV/SiCXhdc8ovGlyuyUXh2qIs+MOU1Tip+OyQcjr7subu4Io0P+1sxRS8flOEFganSDQhsoIc+FvWgyjO2BUyYXym+9BmPmbxdP6GGJq1k77kstUe0cIvRFU16Mq1pwmg2gexGBPlT0jdpHpZltLyFiEYAOqnG58kr5/ZsAfM1plRrT29G2qP0N+z8D3fjwUb5xpHKhfWfKYtaOCOvPSVVzuH6dVqUoO/MZ+b6XBRzg+ZF0K4AsGf2W/OUbdsyHxi3tX9QJY42WumZfEoARhQrNcNBNuteKmm+JL5gn5FvIqg2jU/3pdbTImqQq2QNkyTEGjYgvojvPLNBq55zwAAAAAA", "u": ""}, {"id": "3", "e": 1, "w": 16, "h": 12, "p": "data:image/webp;base64,UklGRqQAAABXRUJQVlA4WAoAAAAQAAAADwAACwAAQUxQSGUAAAANcBDbtpVsErwhAREYEkADDwl8DcQm0uDZ4NiACM8CjhEgwz9CREwAF9rpxE3LcKiDR6/5GO0Y8zGqd0o77YrwKuJWLwWA+7ObV7YbqYL6o8zmZJdSHaYxbQDZXqRfEXeAv/2NAwBWUDggGAAAADABAJ0BKhAADAABQCYlpAADcAD+/TZoAA==", "u": ""}, {"id": "4", "e": 1, "w": 24, "h": 24, "p": "data:image/webp;base64,UklGRs4AAABXRUJQVlA4WAoAAAAQAAAAFwAAFwAAQUxQSIIAAAABYEhbU6YPYRBCCCGEEEIYhBBCCGERQhiERViD3+nMhxARCtw2akjYG34BR1IBQznue8RB2nn/1Ci06/ZcEuXr3jgQMXc1VPemBHbCQkje7LHJvBkAt6MBIHccjOosn9yOpzDm3wmA3LFSyvIfR+64wNHvW0mb/zhuRwOrdVaxDJYKVlA4ICYAAADQAgCdASoYABgAPm00lkekIyIhKAgAgA2JaQAAPaOgAP77nMAAAA==", "u": ""}, {"id": "5", "e": 1, "w": 15, "h": 11, "p": "data:image/webp;base64,UklGRogAAABXRUJQVlA4WAoAAAAQAAAADgAACgAAQUxQSEoAAAABb2CQbaQz+CLMX/MlIiInnziwiWzbyX5aTOCBGiW5xwIWkBC9ICI+N4iI6H8AGXDkoHgrcMYCQ8QMV0wJR0wJxTsmOGsw1EDpD1ZQOCAYAAAAMAEAnQEqDwALAAFAJiWkAANwAP79NmgA", "u": ""}, {"id": "6", "e": 1, "w": 21, "h": 23, "p": "data:image/webp;base64,UklGRsQAAABXRUJQVlA4WAoAAAAQAAAAFAAAFgAAQUxQSHgAAAABcIhtkxD9CIMQwiCEEEIIIYQQQgiLEMIihDAGX3e3z0zPEUTEBOgvt+sND/aadcBLJ8BthYVhy00xui3VGXZTdmd4Kj0zXJW2+IlZ+QugT8pvAM2Ud4BDRbshVlUbhKu6QjdVPThNVetsqh+xqL50V31qphddrwJWUDggJgAAANACAJ0BKhUAFwA+bTSWR6QjIiEoCACADYlpAAA9o6AA/vucwAAA", "u": ""}, {"id": "7", "e": 0, "p": "/Users/<USER>/Desktop/Brand Jar/so true/Sotrue_all_1s/Sotrue_text_1s/(Footage)/wow#01.wav", "u": ""}]}