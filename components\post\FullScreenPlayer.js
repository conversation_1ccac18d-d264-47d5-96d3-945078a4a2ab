import {Platform, StyleSheet, Text, View, Dimensions} from 'react-native';
import React, {useCallback, useEffect, useState} from 'react';
import Video from 'react-native-video';
import {hasImageUrlExist} from '../../utils/Utils';
import ReelProgressBar from '../videoContents/ReelProgressBar';

const FullScreenPlayer = ({
  videoPlayerRef = null,
  media_cover = null,
  media_file = null,
  playVideo = true,
  onLoadStart,
  onLoad,
  onBuffer,
  height = 20,
  style = {},
  insets = null,
  onSliderValueComplete = null,
  isLandscape = false,
}) => {
  const [pausedVideo, setPausedVideo] = useState(playVideo);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [videoNaturalSize, setVideoNaturalSize] = useState({
    width: 0,
    height: 0,
  });

  useEffect(() => {
    setPausedVideo(playVideo);
  }, [playVideo]);

  // Get screen dimensions
  const screenDimensions = Dimensions.get('window');
  const screenWidth = screenDimensions.width;
  const screenHeight = screenDimensions.height;

  // Calculate video container style based on orientation and video aspect ratio
  const getVideoContainerStyle = () => {
    // Always center the video and let resizeMode handle the aspect ratio
    return {
      width: screenWidth,
      height: height,
      alignSelf: 'center',
      backgroundColor: '#000000', // Black background for any empty space
    };
  };

  const [videoCompletedPercentage, setVideoCompletedPercentage] = useState(0);
  const onVideoProgress = useCallback(e => {
    setCurrentTime(e.currentTime);
    // const currentTime = parseFloat(e.currentTime);
    // const playableDuration = parseFloat(e.playableDuration)
    // const percentage = (currentTime / playableDuration) * 100;
    // setVideoCompletedPercentage(percentage);
  }, []);
  const onVideoLoad = useCallback(data => {
    if (onLoad) {
      onLoad(data);
    }
    setDuration(data.duration);

    // Capture video natural dimensions for centering calculations
    if (data.naturalSize) {
      setVideoNaturalSize({
        width: data.naturalSize.width,
        height: data.naturalSize.height,
      });
    }
  }, []);
  const onSliderStart = () => {
    setPausedVideo(true);
  };
  const onSliderValueCompleteChange = value => {
    if (videoPlayerRef) {
      videoPlayerRef.current.seek(value);
      setPausedVideo(false);
      if (onSliderValueComplete) {
        onSliderValueComplete();
      }
    }
  };

  return (
    <>
      <Video
        ref={videoPlayerRef}
        poster={hasImageUrlExist(media_cover) ? media_cover : null}
        posterResizeMode={'cover'}
        source={{uri: media_file, cache: false}}
        resizeMode="contain"
        repeat={true}
        paused={pausedVideo}
        muted={false}
        onLoadStart={onLoadStart}
        onProgress={e => onVideoProgress(e)}
        onLoad={onVideoLoad}
        onBuffer={onBuffer}
        // onError={(e) => console.log(e)}
        // onEnd={() => onVideoEnd()}
        fullscreenAutorotate={true}
        autoPlay={true}
        playInBackground={false}
        ignoreSilentSwitch={'ignore'}
        style={{...style, ...getVideoContainerStyle()}}
      />
      <View
        style={[
          styles.reelProgressBarContainer,
          Platform.OS == 'ios'
            ? {bottom: insets.bottom + 5, zIndex: 9999}
            : {top: height - 15},
        ]}>
        <ReelProgressBar
          value={videoCompletedPercentage}
          currentTime={currentTime}
          onSliderStart={onSliderStart}
          onSliderValueCompleteChange={onSliderValueCompleteChange}
          duration={duration}
        />
      </View>
    </>
  );
};

export default FullScreenPlayer;

const styles = StyleSheet.create({
  reelProgressBarContainer: {
    position: Platform.OS == 'ios' ? 'relative' : 'absolute',
    // top:100,
    left: 0,
    right: 0,
    zIndex: 999,
  },
});
