export const DEFAULT_THEME_COLOR = {
  primaryColor: '#F2987B',
  accentColor: '#F2987B', //#74329C
  primaryTextColor: '#000000',

  loginModuleBackBtnTint: '#AAB2B7',

  textInputBackgroundColor: '#FFFFFF',

  oldLoginBackground: '#F2987B',

  // Check Color
  darkGray: '#646D7E',

  backgroundColor: '#E5E5E5',
  statusBarColor: '#E5E5E5',

  editTextBackgroundColor: 'transparent',

  appBarBackgroundColor: 'transparent',
  topHeaderBottomColor: '#CCC',
  topHeaderColor: '#F2987B',

  mainHeadingColor: '#F2987B',

  extraBackgroundColor: '#F2EBE9',

  successColor: '#52c41a',
  errorColor: '#ff3232', //ff190c ff603d
  warningColor: '#ffbb33',
  //#bd6a4f
  infoColor: '#33b5e5',

  //Text Input color
  // inputPlaceholderColor: "rgba(60, 60, 67, 0.3)",
  inputPlaceholderColor: '#171F24',
  inputTextColor: '#000000', //"#F2987B"
  inputDisabledTextColor: '#00000023',
  inputPrimaryColor: '#F2987B', //#F2987B
  inputOutlineColor: 'rgba(60, 60, 67, 0.3)',
  inputLabelColor: '#000000',

  formInputTextColor: '#000000',
  formBackgroundColor: '#FFFFFF',

  buttonBackColor: 'transparent',
  buttonTextColor: '#F2987B',
  buttonBorderColor: '#F2987B',

  darkGray: '#646D7E',

  rejectBoxColor: '#ff603d',

  iconTintColorDark: '#7F8A96',

  /////////////////////////////////////
  transparentColor: 'transparent',
  placeholderBackgroundColor: '#ccc',
  placeholderForegroundColor: '#999',

  ///////////////////////////////////////
  tabActiveBootomBorderColor: '#F2987B',
  tabActiveColor: '#F2987B',
  tabInActiveColor: '#C5C5C7',

  dropdownActiveColor: '#F2987B',
  dropdownActiveBorder: '#F2987B',
  dropdownInActiveColor: 'grey',

  bottomSheetColor: '#FFFFFF',

  actionSPBackColor: '#F2987B',
  actionSPBackDisableColor: '#F2987B40',
  actionSPColor: '#FFFFFF',

  actionSSBackColor: '#F2EBE9',
  actionSSBackDisableColor: '#F2EBE940',
  actionSSColor: '#F2987B',

  veriGradient1stColor: '#FDAA6A99',
  veriGradient2ndColor: '#FC6568',

  pressableColor: '#F2987B50',
  bodyTextColor: '#000000',

  borderBottomColor: '#CCC',

  pressableRippleColor: '#F2987B40',

  profileCatBorderColor: '#E59D80',
  profileCatActiveColor: '#E59D80',

  profileCatInActiveColor: '#C1C0C8',

  /////////////////////////
  listRowTextColor: '#F2987B',
  listRowIconTintColor: '#F2987B',

  cancelBtnBackground: '#F2EBE9',
  cancelBtnText: '#F2987B',
  cancelBtnBorder: '#F2EBE9',
  submitBtnBackground: '#F2987B',
  submitBtnBorder: '#F2987B',
  disableSubmitBtnBackground: '#F2987B40',
  submitBtnText: '#FFFFFF',

  bottomSheetBtnBackground: '#F2987B',
  bottomSheetBtnText: '#FFFFFF',

  spinnerBackground: '#000',
  spinnerText: '#FFF',
  snackbarSuccessBackground: '#55e200',
  snackbarSuccessText: '#000000',
  snackbarFailureBackground: '#ff3232',
  snackbarFailureText: '#FFFFFF',

  /////////////
  googleSignBtnBackground: '#F2EBE9',
  googleSignBtnText: '#F2987B',

  ///////////////////
  loginSignupGradient1: '#E59D80',
  loginSignupGradient2: '#d89d89',
  loginSignupGradient3: '#759fd2',

  /////////////////
  notiHeaderText: '#171F24',
  notiHeaderViewAllText: '#AAB2B7',

  primaryButtonText: '#FFFFFF',
  //////////
  searchBarBackground: '#F2EBE9',
  searchBarText: '#F2987B',
  /////////////////
  modalBackground: 'rgba(255, 255, 255, 0.1)',
  modalContainerBackground: '#FFFFFF',
  awesomeText: '#F2987B',
  awesomeBodyText1: '#000000',
  awesomeBodyText2: '#F2987B',
  shareItNowText: '#000000',
  shareSocialTintIcon: '#0000004D',

  //////////////
  errorBoxBackground: '#F2EBE9',
  errorBoxText: '#C5C5C7',

  /////////////
  followedBoxBackground: '#F2EBE9',
  followedBoxText: '#F2987B',
  introPopupIconTint: '#FFFFFF',

  notiProfileImageBoxBackground: '#DDDDDD',

  commentFooterContainerBackground: '#FFFFFF',
  commentInputBoxBorderColor: '#000000',
  postBtnColor: '#F2987B',
  commentsText: '#000000',

  threeDotMenuDivider: '#CCC',
  threeDotMenuText: '#F2987B',

  /////////////
  unlockPopupHeadText: '#F2987B',
  unlockPopupBodyTxt: '#000000',
  unlockPopupWarringTxt: '#52c41a',
  unlockPopupBtnBackground: '#F2EBE9',
  unlockPopupBtnText: '#F2987B',
  unlockPopupRefreshBtnText: '#F2987B',

  signUpBtnBackground: '#E59D80',

  postProgressBarBackground: '#030001',
  postTypeButtonBackground: '#FFFFFF',

  switchTextColor: '#000000',

  homeBottomTabActiveColor: '#F2987B',
  homeBottomTabInActiveColor: '#FFFFFF',

  postActiveIconColor: '#F2987B',
  postInActiveIconColor: '#FFFFFF',

  bottomTabBackground: '#000000',
  fullScreenIconTintColor: '#FFFFFF',
  dropdownTextColor: '#FFFFFF',
  dropdownBorderColor: '#FFFFFF',
  dropdownSelectedTextColor: '#FFFFFF',
  dropdownPlaceHolderColor: '#CCC',
  dropdownLabelColor: '#FFFFFF',
  copyTextBackground: '#373737',
  copyTextBtnColor: '#FFFFFF',

  playlistBannerTitleColor: '#FFFFFF',
  playlistBannerYearCountColor: '#FFFFFF',
  playlistBannerDescColor: '#FFFFFF',

  buttonGroupBackColor: '#373737',
  buttonGroupBorderColor: '#373737',
  buttonGroupInActiveTextColor: '#FFFFFF',
  buttonGroupActiveTextColor: '#FFFFFF',

  switchInActiveC: '#383b37',
  switchInActiveThumbC: '#50524e',

  totalEaringsVal: '#FFFFFF',
  totalEaringsValTxt: '#FFFFFF',
  dropIconTintColor: '#FFFFFF',
  listItemIcon: '#111111',
  accVerifiedFlushMsg: '#111111',
  progressBarItemIcon: '#111111',

  fileInputBoxBorderC: '#FFFFFF',
  fileInputBoxActionIconTintColorC: '#FFFFFF',

  profileImageBoxGradientOne: '#00000050',
  profileImageBoxGradientTwo: '#00000050',

  profileCountLabelC: '#FFFFFF',
  profileCountValueC: '#FFFFFF',
  profileBoxHeaderOptionIconC: '#FFFFFF',
  profileBoxNameC: '#FFFFFF',
  profileBoxIdC: '#FFFFFF',
  profileBoxBioC: '#FFFFFF',
  PItitleC: '#FFFFFF',

  playlistLCOne: '#111111',
  playlistLCTwo: '#00000070',
  playlistLCThree: '#11111170',
  playlistLCFour: '#000',

  homeShowCardText: '#FFFFFF',
  homeShowCardTextShadow: 'rgba(0, 0, 0, 0.75)',

  optionSelectionItemColor: '#768390',
  selectBoxSearchIconTintColor: '#CCCCCC',
  searchBoxBorder: '#707070',
  dateTextColor: '#FFFFFF',

  shareSocialIconTintColor: '#FFFFFF',
  shareSocialTintIconTintColor: '#FFFFFF4D',

  errorPopupMsgColor: '#707070',
};
export default Colors = DEFAULT_THEME_COLOR;
