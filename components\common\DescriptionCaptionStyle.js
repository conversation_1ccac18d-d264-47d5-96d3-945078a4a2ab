import React, {useContext, useEffect, useState} from 'react';
import {StyleSheet, View, Text} from 'react-native';
import {TAGGED_SYMBOL} from '../../utils/Appconfig';
import EntutoTextView from './EntutoTextView';
import {useNavigation} from '@react-navigation/native';
import {AppStateContext} from '../..';
import useSThemedStyles from '../../theme/useSThemedStyles';

const DescriptionCaptionStyle = ({
  mainText,
  validHandleList,
  mainTextStyle = {},
  highlightStyle = {},
  descType = 'POST',
  extraFunction,
  numberOfLines = 2,
  ...props
}) => {
  const navigation = useNavigation();
  const [validList, setValidList] = useState([]);
  const [expanded, setExpanded] = useState(false);
  const [isTruncated, setIsTruncated] = useState(false);
  const [trimmedText, setTrimmedText] = useState('');
  const {fullUserDetails} = useContext(AppStateContext);
  const style = useSThemedStyles(styles);

  const __profile_seq = fullUserDetails._profile_seq ?? -1;

  useEffect(() => {
    if (validHandleList) {
      setValidList(validHandleList.map(obj => obj.handle));
    }
  }, [validHandleList]);

  const userHandlePress = userHandle => {
    let profileSeq = -1;
    validHandleList.forEach(obj => {
      if (obj.handle === userHandle) profileSeq = obj.profile_seq;
    });
    if (profileSeq < 0) return;

    if (profileSeq === __profile_seq) {
      navigation.navigate('HomeScreen', {screen: 'ProfileFeed'});
    } else {
      if (descType === 'STORY') extraFunction?.();
      navigation.navigate('OthersProfileScreen', {profileSeq});
    }
  };

  const toggleExpand = () => setExpanded(prev => !prev);

  const processText = text =>
    text.split(' ').map((word, idx, arr) => {
      const suffix = idx < arr.length - 1 ? ' ' : '';
      const isHandle =
        word.charAt(0) === TAGGED_SYMBOL && validList.includes(word);
      const textStyles = [
        style.primaryText,
        isHandle ? style.highlightText : null,
        isHandle ? highlightStyle : mainTextStyle,
      ];

      return (
        <Text
          key={idx}
          style={[
            style.primaryText,
            isHandle ? style.highlightText : null,
            isHandle ? highlightStyle : mainTextStyle,
          ]}>
          {word}
          {suffix}
        </Text>
      );
    });

  const handleTextLayout = ({nativeEvent: {lines}}) => {
    if (expanded) return;
    if (lines.length > numberOfLines) {
      setIsTruncated(true);
      const firstLines = lines
        .slice(0, numberOfLines)
        .map(l => l.text)
        .join('');
      setTrimmedText(firstLines.trimEnd());
    }
  };

  const renderContent = () => {
    if (expanded) {
      return (
        <EntutoTextView style={[style.primaryText, mainTextStyle]}>
          {processText(mainText)}
          <EntutoTextView style={style.lessText} onPress={toggleExpand}>
            {' '}
            …less
          </EntutoTextView>
        </EntutoTextView>
      );
    }

    return (
      <EntutoTextView
        style={[style.primaryText, mainTextStyle]}
        onTextLayout={handleTextLayout}
        numberOfLines={expanded ? undefined : numberOfLines}>
        {isTruncated ? (
          <>
            {processText(trimmedText)}
            <EntutoTextView style={style.moreText} onPress={toggleExpand}>
              ...more
            </EntutoTextView>
          </>
        ) : (
          processText(mainText)
        )}
      </EntutoTextView>
    );
  };

  return <View>{renderContent()}</View>;
};

export default DescriptionCaptionStyle;

const styles = theme =>
  StyleSheet.create({
    primaryText: {
      color: theme.colors.primaryTextColor,
    },
    highlightText: {
      color: theme.colors.descriptionBoxText,
    },
    moreText: {
      color: '#F2987B',
      fontWeight: '500',
    },
    lessText: {
      color: theme.colors.primaryColor,
      fontWeight: '500',
    },
  });
