import {
  <PERSON><PERSON><PERSON><PERSON>,
  FlatList,
  Image,
  Platform,
  StatusBar,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import React, {
  useCallback,
  useContext,
  useEffect,
  useRef,
  useState,
} from 'react-native';
import RItemComponent from '../components/videoContents/RItemComponent';
import CustomStatusBar from '../components/common/CustomStatusBar';
import {TouchableOpacity} from 'react-native';
import {SwiperFlatList} from 'react-native-swiper-flatlist';
import Dimensions from '../constants/Dimensions';
import SuccessFailureMsgBox from '../components/common/SuccessFailureMsgBox';
import ServerConnector from '../utils/ServerConnector';
import {_RedirectionErrorList} from '../utils/Appconfig';
import {RedirectionUrlFunction} from '../utils/RedirectionUrl';
import PostCard from '../components/post/PostCard';
import useOrientationManager from '../hooks/useOrientationManager';
import {AppStateContext} from '..';
import {ActivityIndicator} from 'react-native-paper';
import Video from 'react-native-video';
import {checkValueLength, hasImageUrlExist} from '../utils/Utils';
import Reels from '../components/videoContents/Reels';
import LinearGradient from 'react-native-linear-gradient';
import useDefaultStyle from '../theme/useDefaultStyle';
import ReelsItem from '../components/videoContents/ReelsItem';
import useSTheme from '../theme/useSTheme';
import ReelsView from '../components/reelsView/ReelsView';

const VideoContentScreen = ({route, navigation}) => {
  const theme = useSTheme();
  const {defaultStyle} = useDefaultStyle();
  const {postSeq, postProfileSeq, cameFrom} = route.params;
  const [postList, setPostList] = useState([]);
  const RowsPerPage = 10; //Don't change this value this is fixed
  const [startRecordV, setStartRecordV] = useState(0);
  const [errorMsg, setErrorMsg] = useState('');
  const [progressLoading, setProgressLoading] = useState(true);
  const [currentVisibleIndex, setCurrentVisibleIndex] = useState(0);
  const {changeUserDetails, fullUserDetails, changeUserProfileImage} =
    useContext(AppStateContext);
  const __profile_seq = fullUserDetails.hasOwnProperty('_profile_seq')
    ? fullUserDetails._profile_seq
    : -1;
  const [isNoDataFound, setIsNoDataFound] = useState(false);
  const [currentReelIndex, setCurrentReelIndex] = useState(0);
  const [refreshList, setRefreshList] = useState(Math.random());
  const backBtnPress = () => {
    if (navigation.canGoBack()) {
      navigation.goBack(null);
    } else {
      navigation.replace('HomeScreen');
    }
  };
  function handleBackButtonClick() {
    backBtnPress();
    return true;
  }

  console.log('route.params', route.params);

  useEffect(() => {
    BackHandler.addEventListener('hardwareBackPress', handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        'hardwareBackPress',
        handleBackButtonClick,
      );
    };
  }, []);

  // Handle orientation locking based on cameFrom parameter
  useOrientationManager(cameFrom === 'EPISODE', [cameFrom]);
  useEffect(() => {
    if (cameFrom == 'EPISODE') {
      if (route.params.hasOwnProperty('seasonSeq')) {
        console.log('from episode', route.params.seasonSeq);
        setProgressLoading(true);
        getEpisodeClipListService(route.params.seasonSeq);
      }
    } else {
      if (cameFrom == 'MOBILE_OTP' || cameFrom == 'PERSONALIZATION') {
        setProgressLoading(true);
        getUserProfileService();
      } else {
        setProgressLoading(true);
        getHomePostListService(0, RowsPerPage);
      }
    }
  }, []);

  const postCardClick = () => {};
  const keyExtractor = useCallback(item => `${item.post_seq}`);
  const renderItem = ({item, index}) => {
    return (
      // <View style={{ height: Dimensions.screenHeight, width: Dimensions.screenWidth }}>
      //     <Video
      //         poster={hasImageUrlExist(item.media_cover) ? item.media_cover : null}
      //         posterResizeMode={'cover'}
      //         source={{ uri: item.media_file }}
      //         resizeMode='cover'
      //         repeat={true}
      //         paused={currentVisibleIndex != index}
      //         muted={false}
      //         onError={(e) => console.log(e)}
      //         style={styles.NSPostVideo}
      //     />
      // </View>

      <PostCard
        rowIndex={index}
        currentVisibleIndex={currentVisibleIndex}
        itemData={item}
        navigation={navigation}
        isMyProfile={__profile_seq == item.profile_seq}
        postCardClick={postCardClick}
        fullScreen={true}
        repeatVideo={true}
        showVideoContent={true}
      />
    );
  };
  function getEpisodeClipListService(seasonSeq) {
    let hashMap = {
      _action_code: '11:GET_PLAYLIST_CLIPS',
      season_seq: seasonSeq,
      // sel_post_seq: postSeq,
    };
    let connector = new ServerConnector();
    connector.postData(
      hashMap,
      data => {
        // success method
        data.data.map((obj, i) => {
          obj.display_name = data.display_name;
          obj.profile_picture = data.profile_picture;
          obj.user_handle = data.user_handle;
          obj.cover_image = data.cover_image;
          obj.enable_comment = data.enable_comment;
          obj.enable_watermark = data.enable_watermark;
          obj.story_count = data.story_count;
          obj.is_verified = data.is_verified;
          obj.profile_seq = postProfileSeq;
          obj.is_restricted = data.is_restricted;
          if (String(obj.post_seq) == String(postSeq)) {
            setCurrentReelIndex(i);
          }
        });
        setRefreshList(Math.random());
        setPostList([...[], ...data.data]);

        setIsNoDataFound(false);
        setProgressLoading(false);
      },
      (errorCode, errorMessage, data) => {
        // failure method
        // console.log("Error", errorMessage)
        if (_RedirectionErrorList.includes(errorCode)) {
          setProgressLoading(false);
          RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
        } else {
          setPostList([]);
          setProgressLoading(false);
          setIsNoDataFound(true);
        }
      },
    );
  }
  function getHomePostListService(startRecord, rowsPerPage) {
    setStartRecordV(startRecord);
    let hashMap = {
      _action_code: '11:GET_POSTS',
      media_type: 'VIDEO',
      _start_row: startRecord,
      _rows_page: rowsPerPage,
    };

    if (cameFrom == 'PROFILE') {
      hashMap._action_code = '11:GET_POSTS_USER';
      hashMap.req_profile_seq = postProfileSeq;
      hashMap.media_type = 'VIDEO';
    }
    if (cameFrom == 'POST' || cameFrom == 'PROFILE' || cameFrom == 'SEARCH') {
      if (parseInt(startRecord) == 0) {
        hashMap.first_seq = postSeq;
      }
    }
    let connector = new ServerConnector();
    connector.postData(
      hashMap,
      data => {
        // success method
        if (cameFrom == 'PROFILE') {
          data.data.map((obj, i) => {
            obj.display_name = data.display_name;
            obj.profile_picture = data.profile_picture;
            obj.user_handle = data.user_handle;
            obj.cover_image = data.cover_image;
            obj.enable_comment = data.enable_comment;
            obj.enable_watermark = data.enable_watermark;
            obj.story_count = data.story_count;
            obj.is_verified = data.is_verified;
            obj.profile_seq = postProfileSeq;
            obj.is_restricted = data.is_restricted;
          });
        }
        if (parseInt(startRecord) == 0) {
          setRefreshList(Math.random());
          setPostList([...[], ...data.data]);
        } else {
          setPostList([...postList, ...data.data]);
        }
        setIsNoDataFound(false);
        setProgressLoading(false);
      },
      (errorCode, errorMessage, data) => {
        // failure method
        // console.log("Error", errorMessage)
        if (_RedirectionErrorList.includes(errorCode)) {
          setProgressLoading(false);
          RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
        } else {
          if (parseInt(startRecord) == 0) {
            setPostList([]);
          }
          setProgressLoading(false);
          setIsNoDataFound(true);
        }
      },
    );
  }
  const onViewableItemsChanged = ({viewableItems}) => {
    if (viewableItems.length >= 1) {
      viewableItems.map(obj => {
        if (obj.isViewable) {
          setCurrentVisibleIndex(obj.index);
        }
      });
    }
  };

  const viewabilityConfigCallbackPairs = useRef([{onViewableItemsChanged}]);
  const onChangeIndex = ({index, prevIndex}) => {
    setCurrentVisibleIndex(index);
    // console.log({ index, prevIndex });
  };
  const onReelsEnd = lastIndex => {
    if (cameFrom != 'EPISODE') {
      if (!isNoDataFound) {
        let startRec = startRecordV + RowsPerPage;
        getHomePostListService(startRec, RowsPerPage);
      }
    }
    setCurrentReelIndex(lastIndex);
  };
  const onSwipeRefresh = cIndex => {
    if (cameFrom != 'EPISODE') {
      setProgressLoading(true);
      setRefreshList(Math.random());
      setCurrentReelIndex(cIndex);
      getHomePostListService(0, RowsPerPage);
    }
  };
  function getUserProfileService() {
    let hashMap = {
      _action_code: '11:GET_USER_PROFILE',
      req_profile_seq: __profile_seq,
    };

    let connector = new ServerConnector();
    connector.postData(
      hashMap,
      data => {
        // success method

        let display_name = '-';
        if (data.data[0].display_name !== null) {
          display_name = data.data[0].display_name;
        }
        let hasBankDetails = 'NO';
        if (data.data[0].bank_account !== null) {
          if (data.data[0].bank_account.length !== 0) {
            hasBankDetails = 'YES';
          }
        }
        let hasStateCity = 'NO';
        if (data.data[0].state !== null) {
          if (data.data[0].state.length !== 0) {
            hasStateCity = 'YES';
          }
        }
        let userDeatails = {
          _user_handle: data.data[0].user_handle,
          _user_account_type: data.data[0].type,
          _user_display_name: display_name,
          _has_bank_details: hasBankDetails,
          _is_profile_verified: data.data[0].is_verified,
          _has_state_city: hasStateCity,
          _profile_picture: data.data[0].profile_picture,
          _cover_image: data.data[0].cover_image,
        };
        let uiColor = 'COLOR_1';
        if (data.data[0].hasOwnProperty('ui_colour')) {
          if (checkValueLength(data.data[0].ui_colour)) {
            uiColor = data.data[0].ui_colour;
          }
        }
        theme.changeThemeColor(uiColor);
        // setShowLoading(false);
        changeUserProfileImage(data.data[0].profile_picture);
        changeUserDetails(userDeatails);
        getHomePostListService(0, RowsPerPage);
      },
      (errorCode, errorMessage, data) => {
        // failure method
        getHomePostListService(0, RowsPerPage);
      },
    );
  }
  return (
    <View style={{flex: 1, position: 'relative', backgroundColor: 'yellow'}}>
      {/* <CustomStatusBar translucent={true} hidden={false} /> */}
      <StatusBar hidden={true} />
      <View style={styles.mediaBackBtnBox}>
        <TouchableOpacity onPress={() => backBtnPress()}>
          <Image
            style={{
              ...styles.mediaBackBtn,
              tintColor:
                errorMsg.length != 0
                  ? '#000000'
                  : progressLoading
                  ? '#FFFFFF'
                  : '#FFFFFF',
            }}
            source={require('../assets/Images/icon/single_post_back.png')}
            resizeMode="contain"
          />
        </TouchableOpacity>
      </View>
      {errorMsg.length != 0 ? (
        <View style={defaultStyle.errorBoxOutside}>
          <SuccessFailureMsgBox
            visibleAllTime={true}
            alertMsg={errorMsg}
            alertKey={errorMsg}
          />
        </View>
      ) : null}
      {progressLoading ? (
        <View style={{flex: 1, backgroundColor: '#000'}}>
          <View style={styles.playBtnBox}>
            <ActivityIndicator size={32} />
          </View>
        </View>
      ) : (
        <View style={{flex: 1}}>
          {/* <View style={styles.playBtnBox}>
                            <ActivityIndicator size={32} />
                        </View> */}
          {/* <ReelsItem videoUrl={itemList[0]['url']} /> */}
          {/* <SwiperFlatList
                    data={postList}
                    // windowSize={4}
                    keyExtractor={keyExtractor}
                    // initialNumToRender={0}
                    // maxToRenderPerBatch={2}
                    // removeClippedSubviews
                    renderItem={renderItem}
                    pagingEnabled
                    decelerationRate={'normal'}
                    // estimatedItemSize={Dimensions.screenHeight}
                    // viewabilityConfigCallbackPairs={viewabilityConfigCallbackPairs.current}
                    viewabilityConfig={{
                        itemVisiblePercentThreshold: 50
                    }}
                    vertical
                    onChangeIndex={onChangeIndex}
                // onScroll={(e) => {
                //     const index = Math.round(
                //         e.nativeEvent.contentOffset.y / Dimensions.screenHeight
                //     );
                //     setCurrentVisibleIndex(index);
                // }}
                /> */}
          {postList.length != 0 ? (
            <Reels
              key={refreshList}
              dataReels={postList}
              onReelsEnd={onReelsEnd}
              navigation={navigation}
              onSwipeRefresh={onSwipeRefresh}
              cameFrom={cameFrom}
              currentReelIndex={currentReelIndex}
              currentProfileSeq={__profile_seq}
            />
          ) : null}
          {/* <ReelsView postList={postList} onReelsEnd={onReelsEnd}
                            navigation={navigation}
                            cameFrom={cameFrom} onSwipeRefresh={onSwipeRefresh} /> */}

          {/* <FlatList
                    data={postList}
                    pagingEnabled
                    renderItem={({ item, index }) => (
                        <RItemComponent data={item} isActive={currentVisibleIndex === index} />
                    )}
                    onScroll={e => {
                        const index = Math.round(
                            e.nativeEvent.contentOffset.y / (Dimensions.screenHeight - 56),
                        );
                        setCurrentVisibleIndex(index);
                    }}
                /> */}
        </View>
      )}
    </View>
  );
};

export default VideoContentScreen;

const styles = StyleSheet.create({
  mediaBackBtnBox: {
    position: 'absolute',
    top: Platform.OS == 'ios' ? 48 : 32,
    left: 24,
    zIndex: 3,
  },
  mediaBackBtn: {
    height: 20,
    width: 20,
    marginEnd: 5,
    opacity: 0.8,
    zIndex: 3,
  },
  NSPostVideo: {
    position: 'absolute',
    top: 0,
    left: 0,
    bottom: 0,
    right: 0,
  },
  videoOverlayGrad: {
    position: 'absolute',
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
    zIndex: 2,
  },
  playBtnBox: {
    height: 63,
    width: 63,
    position: 'absolute',
    top: '43%',
    left: '43%',
    backgroundColor: '#00000080',
    borderRadius: 15,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.5,
    shadowRadius: 1.41,

    // elevation: 2,
  },
});
