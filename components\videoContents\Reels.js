import {Dimensions, StyleSheet, Text, View} from 'react-native';
import React from 'react';
import {useState} from 'react';
import GestureRecognizer from 'react-native-swipe-gestures';
import PostCard from '../post/PostCard';

const SCREEN_WIDTH = Dimensions.get('window').width;
const SCREEN_HEIGHT = Dimensions.get('window').height;
const Reels = ({
  dataReels,
  currentReelIndex = 0,
  onReelsEnd = null,
  onSwipeRefresh,
  navigation,
  currentProfileSeq = '',
  cameFrom = '',
  ...props
}) => {
  const [currentIndex, setCurrentIndex] = useState(currentReelIndex);
  const reel = dataReels.length ? dataReels[currentIndex] : {};
  const [refreshListData, setRefreshListData] = useState(Math.random());
  const [scrollEnable, setScrollEnable] = useState(true);

  const nextStory = () => {
    if (dataReels.length - 1 > currentIndex) {
      setCurrentIndex(currentIndex + 1);
      setRefreshListData(Math.random());
    } else {
      onReelsEnd(currentIndex + 1);
      //setCurrentIndex(0);
      //props.onStoryNext(false);
    }
  };
  const prevStory = () => {
    if (currentIndex > 0 && dataReels.length) {
      setCurrentIndex(currentIndex - 1);
      setRefreshListData(Math.random());
    } else {
      onSwipeRefresh(0);
      setCurrentIndex(0);
      //props.onStoryPrevious(false);
    }
  };
  const config = {
    velocityThreshold: 0.1,
    directionalOffsetThreshold: 80,
  };
  const onSwipeDown = () => {
    if (scrollEnable) {
      prevStory();
    }
  };

  const onSwipeUp = () => {
    if (scrollEnable) {
      nextStory();
    }
  };
  const postCardClick = (clickID, obj) => {
    if (clickID == 'OPEN_UNLOCK_POPUP') {
      setScrollEnable(prevState => false);
    }
    if (clickID == 'CLOSE_UNLOCK_POPUP') {
      setScrollEnable(prevState => true);
    }
  };
  return (
    <GestureRecognizer
      onSwipeDown={onSwipeDown}
      onSwipeUp={onSwipeUp}
      config={config}
      style={styles.container}>
      <PostCard
        key={refreshListData}
        itemData={reel}
        isMyProfile={currentProfileSeq == reel.profile_seq}
        postCardClick={postCardClick}
        fullScreen={true}
        repeatVideo={true}
        showVideoContent={true}
        navigation={navigation}
        cameFrom={cameFrom}
      />
    </GestureRecognizer>
  );
};

export default Reels;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'red',
  },
  contentVideo: {
    width: SCREEN_WIDTH,
    // aspectRatio: 1,
    backgroundColor: 'green',
    flex: 1,
    height: SCREEN_HEIGHT,
  },
});
