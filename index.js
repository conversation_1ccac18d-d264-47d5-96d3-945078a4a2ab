/* global require */
import 'react-native-gesture-handler';
/**  * @format  */
// if (__DEV__) {
//   require('react-native').DevSettings.setIsDebuggingRemotely(true);
// }
import 'react-native-reanimated';
import * as React from 'react';
import {AppRegistry, Platform, SafeAreaView, View, LogBox} from 'react-native';
import {Provider} from 'react-redux';
import {GestureHandlerRootView} from 'react-native-gesture-handler';
import messaging from '@react-native-firebase/messaging';
import {
  DefaultTheme,
  DarkTheme,
  Provider as PaperProvider,
} from 'react-native-paper';
// import jestSetup from 'react-native-gesture-handler/jestSetup';
import App from './App';
import store from './store/store';
import Colors from './constants/Colors';
import ErrorBoundry from './ErrorBoundry';
import SThemeProvider from './theme/SThemeProvider';
import DefaultStyleProvider from './theme/DefaultStyleProvider';
import {name as appName} from './app.json';
import './utils/Default';
import appData from './data/Data';

// Import utilities
import {
  _getAccessKey,
  _getFirstTimeUser,
  _getShowHomeItroIcon,
  _getUserCredential,
  _getUserSearchKeywards,
  _saveUserCredential,
  _updateUserSearchKeywards,
} from './utils/AuthLogin';
import {
  configureReanimatedLogger,
  ReanimatedLogLevel,
} from 'react-native-reanimated';

// This is the default configuration
configureReanimatedLogger({
  level: ReanimatedLogLevel.warn,
  strict: false, // disable warnings
});
LogBox.ignoreLogs([
  "[react-native-gesture-handler] Seems like you're using an old API with gesture components, check out new Gestures system!",
]);
LogBox.ignoreLogs(['new NativeEventEmitter']); // Ignore log notification by message
// ignore specific yellowbox warnings
LogBox.ignoreLogs(['Require cycle:', 'Remote debugger']);
// LogBox.ignoreAllLogs(); //Ignore all log notifications
LogBox.ignoreLogs([
  'Remote debugger is in a background tab which may cause apps to perform slowly',
  'Require cycle: node_modules/rn-fetch-blob/index.js',
  'Require cycle: node_modules/react-native/Libraries/Network/fetch.js',
  "EventEmitter.removeListener('change', ...): Method has been deprecated. Please instead use `remove()` on the subscription returned by `EventEmitter.addListener`.",
  'Failed prop type: Invalid props.style key `resizeMode` supplied to `Video`.',
  "JSON value '<null>' of type NSNull cannot be converted to NSString",
]);

export const AppStateContext = React.createContext('');
export const NotiStateContext = React.createContext('');
export const PageRefreshContext = React.createContext('');
export const SinglePostContext = React.createContext('');

export default function Main() {
  const [fullUserDetails, setfullUserDetails] = React.useState({});
  const [userProfileImage, setuserProfileImage] = React.useState(null);
  const [captureMedia, setcaptureMedia] = React.useState(null);

  const [captureStoryMedia, setcaptureStoryMedia] = React.useState(null);
  const [captureSelfie, setcaptureSelfie] = React.useState(null);
  const [captureImageId, setcaptureImageId] = React.useState(null);
  const [captureSelfieId, setcaptureSelfieId] = React.useState(null);

  const [captureProfileImg, setcaptureProfileImg] = React.useState(null);
  const [captureCoverImg, setcaptureCoverImg] = React.useState(null);
  const [__commentObj, set__commentObj] = React.useState({
    postSeq: -1,
    commentRight: 'NO',
    postCommentRefresh: Math.random(),
  });

  const [newNotificationCame, setnewNotificationCame] = React.useState(false);
  const [newNotificationTypeList, setnewNotificationTypeList] = React.useState(
    [],
  );

  const [selectedTagProfileList, setselectedTagProfileList] = React.useState(
    [],
  );
  const [selectedNotificationMenu, setSelectedNotificationMenu] =
    React.useState(0);
  const [isFirstTimeUser, setIsFirstTimeUser] = React.useState(true);
  const [showHomeIntro, setShowHomeIntro] = React.useState(true);
  const [searchKeywardHistory, setSearchKeywardHistory] = React.useState([]);
  const [acceptTerms, setAcceptTerms] = React.useState(false);
  const [notificationRefresh, setNotificationRefresh] = React.useState(
    Math.random(),
  );
  const [singleProfileObj, setSingleProfileObj] = React.useState({});
  const [showCommentCountV, setShowCommentCountV] = React.useState(false);
  const [homepagePostDataBackup, setHomepagePostDataBackup] = React.useState({
    homePageData: [],
    homeCurrentStartRecord: 0,
    postStatusChangeData: [],
    homePageDataStoreTime: new Date(),
  });
  const [homePageRefresh, setHomePageRefresh] = React.useState('NO');

  const changeUserDetails = dataObject => {
    let newObj = Object.assign(fullUserDetails, dataObject);
    appData._userDetails = newObj;
    setfullUserDetails(newObj);
    _saveUserCredential(newObj);
  };
  const changeCaptureMedia = mediaData => {
    setcaptureMedia(mediaData);
  };
  const changeStoryMedia = mediaData => {
    setcaptureStoryMedia(mediaData);
  };
  const changeVeriSelfieMedia = mediaData => {
    setcaptureSelfie(mediaData);
  };
  const changeVeriImageID = mediaData => {
    setcaptureImageId(mediaData);
  };
  const changeVeriSelfieId = mediaData => {
    setcaptureSelfieId(mediaData);
  };
  const changeCaptureProfileImg = mediaData => {
    setcaptureProfileImg(mediaData);
  };
  const changeCaptureCoverImg = mediaData => {
    setcaptureCoverImg(mediaData);
  };
  const changeCommentObj = commentData => {
    set__commentObj(commentData);
  };
  const changeNewNotficationCame = data => {
    setnewNotificationCame(prev => (prev = data));
  };
  const changeNewNotficationTypeList = data => {
    setTimeout(() => {
      setnewNotificationTypeList([...data]);
    }, 100);
  };
  const changeUserProfileImage = data => {
    setuserProfileImage(data);
  };
  const changeTagProfileList = data => {
    setselectedTagProfileList(data);
  };
  const changeFirstTimeUser = data => {
    setIsFirstTimeUser(data);
  };
  const changeShowHomeIntro = data => {
    setShowHomeIntro(data);
  };
  const changeKeywardHistory = data => {
    _updateUserSearchKeywards(data);
    setTimeout(() => {
      setSearchKeywardHistory([...data]);
    }, 100);
  };
  const changeAcceptTerms = data => {
    setAcceptTerms(data);
  };
  const changeNotificationRefresh = value => {
    setNotificationRefresh(value);
  };
  const changeSingleProfileObj = dataObject => {
    let newObj = Object.assign({}, dataObject);
    setSingleProfileObj(newObj);
  };
  const changeShowCommentCount = data => {
    setShowCommentCountV(data);
  };
  const updateHomePagePostData = data => {
    setHomepagePostDataBackup(prevData => {
      // Update prevData with new data
      return {...prevData, ...data};
    });
  };
  const changeHomePageRefresh = value => {
    setHomePageRefresh(value);
  };
  const [selectedGuide, setSelectedGuide] = React.useState(null);
  const changeSelectedGuide = guideId => {
    setSelectedGuide(guideId);
  };
  const contextValue = {
    fullUserDetails,
    captureMedia,
    captureStoryMedia,
    captureSelfie,
    captureImageId,
    captureSelfieId,
    captureProfileImg,
    captureCoverImg,
    __commentObj,
    newNotificationCame,
    newNotificationTypeList,
    userProfileImage,
    selectedTagProfileList,
    isFirstTimeUser,
    showHomeIntro,
    searchKeywardHistory,
    acceptTerms,
    showCommentCountV,
    homepagePostDataBackup,
    selectedGuide,

    changeUserDetails,
    changeCaptureMedia,
    changeStoryMedia,
    changeVeriSelfieMedia,
    changeVeriImageID,
    changeVeriSelfieId,
    changeCaptureProfileImg,
    changeCaptureCoverImg,
    changeCommentObj,
    changeNewNotficationCame,
    changeNewNotficationTypeList,
    changeUserProfileImage,
    changeTagProfileList,
    changeFirstTimeUser,
    changeShowHomeIntro,
    changeKeywardHistory,
    changeAcceptTerms,
    changeShowCommentCount,
    updateHomePagePostData,
    changeSelectedGuide,
  };
  const changeSelectedNotificationMenu = item => {
    setSelectedNotificationMenu(prev => (prev = item));
  };
  const noticontextValue = {
    selectedNotificationMenu,
    changeSelectedNotificationMenu,
  };
  const pageRefreshContextValue = {
    notificationRefresh,
    homePageRefresh,
    changeNotificationRefresh,
    changeHomePageRefresh,
  };
  const singlePostContextValue = {
    singleProfileObj,
    changeSingleProfileObj,
  };

  React.useEffect(() => {
    _getAccessKey(accessKey => {
      appData.__access_key = accessKey;
    });
    _getUserCredential(data => {
      if (data != null) {
        setfullUserDetails(data);
        appData._userDetails = data;
      }
    });
    _getFirstTimeUser(data => {
      if (data != null) {
        if (data == 'YES') {
          changeFirstTimeUser(false);
        }
      }
    });
    _getShowHomeItroIcon(data => {
      if (data != null) {
        if (data == 'NO') {
          changeShowHomeIntro(false);
        }
      }
    });
    _getUserSearchKeywards(data => {
      setSearchKeywardHistory(data);
    });
  }, []);
  React.useEffect(() => {
    // Prevent Screenshot
    // if (Platform.OS == 'ios') {
    //     ScreenGuardModule.unregister();
    //     ScreenGuardModule.register(
    //         //insert any hex color you want here, default black if null or empty
    //         '#000000',
    //         _ => {
    //             // .....do anything you want after the screenshot
    //         },
    //     )
    // }
  }, []);
  return (
    <ErrorBoundry>
      <SThemeProvider>
        <AppStateContext.Provider value={contextValue}>
          <DefaultStyleProvider>
            <NotiStateContext.Provider value={noticontextValue}>
              <PageRefreshContext.Provider value={pageRefreshContextValue}>
                <SinglePostContext.Provider value={singlePostContextValue}>
                  <Provider store={store}>
                    <GestureHandlerRootView style={{flex: 1}}>
                      <View style={{flex: 1}}>
                        <App />
                      </View>
                    </GestureHandlerRootView>
                  </Provider>
                </SinglePostContext.Provider>
              </PageRefreshContext.Provider>
            </NotiStateContext.Provider>
          </DefaultStyleProvider>
        </AppStateContext.Provider>
      </SThemeProvider>
    </ErrorBoundry>
  );
}
// Register background handler
messaging().setBackgroundMessageHandler(async remoteMessage => {
  // console.log('Message handled in the background!', remoteMessage);
});
AppRegistry.registerComponent(appName, () => Main);
