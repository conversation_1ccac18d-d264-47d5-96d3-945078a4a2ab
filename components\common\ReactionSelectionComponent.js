import React, {useRef, useState, useEffect} from 'react';
import {
  View,
  TouchableOpacity,
  StyleSheet,
  Vibration,
  Animated,
} from 'react-native';
import useSThemedStyles from '../../theme/useSThemedStyles';
import useSTheme from '../../theme/useSTheme';
import EntutoTextView from './EntutoTextView';
import LottieView from 'lottie-react-native';
import Sound from 'react-native-sound';
import LikeBtnComponent from './LikeBtnComponent';

const ReactionSelectionComponent = ({
  emotions = [],
  selectedEmotions = [],
  onEmotionPress = null,
}) => {
  const style = useSThemedStyles(styles);
  const theme = useSTheme();

  const ReactionItem = ({
    emotion,
    isSelected,
    onPress,
  }) => {
    const lottieRef = useRef(null);
    const [sound, setSound] = useState(null);
    const scaleAnim = useRef(new Animated.Value(1)).current;

    // Load the sound when the component mounts
    useEffect(() => {
      if (emotion.audioPath) {
        const soundObject = new Sound(emotion.audioPath, Sound.MAIN_BUNDLE, error => {
          if (error) {
            console.log('Failed to load the sound', emotion.audioPath, error);
            return;
          }
          setSound(soundObject);
        });
      }

      // Release the sound when the component unmounts
      return () => {
        if (sound) {
          sound.release();
        }
      };
    }, [emotion.audioPath]);

    // Function to perform scale animation
    const performScaleAnimation = () => {
      if (isSelected) return;

      Animated.sequence([
        Animated.timing(scaleAnim, {
          toValue: 1.5,
          duration: 500,
          useNativeDriver: true,
        }),
        Animated.delay(500),
        Animated.timing(scaleAnim, {
          toValue: 1,
          duration: 500,
          useNativeDriver: true,
        }),
      ]).start();
    };

    const handlePress = () => {
      if (!isSelected) {
        // Play the lottie animation when clicked
        if (emotion.lottiePath && lottieRef.current) {
          lottieRef.current.reset();
          lottieRef.current.play();
        }

        // Perform scale animation for inactive items only
        performScaleAnimation();

        // Trigger vibration when selected
        emotion.vibrationPattern
          ? Vibration.vibrate(emotion.vibrationPattern)
          : Vibration.vibrate(100);

        // Play the sound if loaded and available
        if (sound) {
          sound.play(success => {
            if (success) {
              console.log('Successfully finished playing:', emotion.audioPath);
            } else {
              console.log('Playback failed for:', emotion.audioPath);
            }
          });
        }
      }

      if (onPress) {
        onPress(emotion);
      }
    };

    return (
      <View style={style.reactionItemContainer}>
        <TouchableOpacity onPress={handlePress}>
          <View
            style={[
              style.reactionIconBox,
              {
                borderColor: isSelected
                  ? theme.colors.primaryColor
                  : 'transparent',
              },
            ]}>
            <Animated.View style={{transform: [{scale: scaleAnim}]}}>
              {emotion.lottiePath ? (
                <LottieView
                  ref={lottieRef}
                  source={emotion.lottiePath}
                  style={style.reactionIcon}
                  loop={false}
                  autoPlay={false}
                />
              ) : (
                <LikeBtnComponent
                  inActiveIcon={emotion.icon}
                  activeIcon={emotion.icon}
                  inActiveTintColor={theme.colors.fullScreenIconTintColor}
                  isLike={isSelected}
                  style={style.reactionIcon}
                />
              )}
            </Animated.View>
          </View>
        </TouchableOpacity>
        <EntutoTextView style={style.reactionLabel}>
          {emotion.label}
        </EntutoTextView>
      </View>
    );
  };

  return (
    <View style={style.reactionsContainer}>
      {emotions.map((emotion, index) => (
        <ReactionItem
          key={emotion.value}
          emotion={emotion}
          isSelected={selectedEmotions.includes(emotion.value)}
          onPress={onEmotionPress}
        />
      ))}
    </View>
  );
};

export default ReactionSelectionComponent;

const styles = theme =>
  StyleSheet.create({
    reactionsContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      marginTop: theme.dimensions.loginModuleInputMT,
    },
    reactionItemContainer: {
      alignItems: 'center',
      marginRight: 16,
      marginBottom: 16,
    },
    reactionIconBox: {
      width: 60,
      height: 60,
      borderRadius: 30,
      borderWidth: 2,
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: theme.colors.backgroundColor,
    },
    reactionIcon: {
      width: 40,
      height: 40,
    },
    reactionLabel: {
      marginTop: 8,
      fontSize: theme.calculateFontSizeNew(12),
      textAlign: 'center',
      color: theme.colors.textColor,
    },
  });
