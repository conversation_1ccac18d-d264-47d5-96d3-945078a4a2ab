{"nm": "happy after 2", "h": 500, "w": 500, "meta": {"g": "@lottiefiles/toolkit-js 0.65.0"}, "layers": [{"ty": 2, "nm": "guide", "sr": 1, "st": 0, "op": 24, "ip": 0, "ln": "616", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [0, 0]}, "s": {"a": 1, "k": [{"s": [100, 100, 100], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 12.255}, {"s": [100, 100, 100], "i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 24}]}, "p": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0.333}, "i": {"x": 0.667, "y": 0.667}, "s": [0, 500, 0], "t": 12.255}, {"s": [0, 500, 0], "t": 24}]}, "r": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [0], "t": 12.255}, {"s": [0], "t": 24}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "refId": "1", "ind": 1}, {"ty": 2, "nm": "smile", "sr": 1, "st": 0, "op": 24, "ip": 0, "ln": "614", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [123.5, 73.852]}, "s": {"a": 1, "k": [{"s": [76, 76, 98.701], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 0}, {"s": [100, 100, 100], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 12.255}, {"s": [100, 100, 100], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 24}]}, "p": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [250.865, 309.571, 0], "t": 0}, {"o": {"x": 0.333, "y": 0.333}, "i": {"x": 0.667, "y": 0.667}, "s": [250.865, 332.571, 0], "t": 5.617}, {"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [250.865, 332.571, 0], "t": 7.149}, {"o": {"x": 0.333, "y": 0.333}, "i": {"x": 0.667, "y": 0.667}, "s": [250.865, 309.571, 0], "t": 12.255}, {"s": [250.865, 309.571, 0], "t": 24}]}, "r": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [0], "t": 12.255}, {"s": [0], "t": 24}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "refId": "2", "ind": 2}, {"ty": 2, "nm": "right eyebrow", "sr": 1, "st": 0, "op": 24, "ip": 0, "ln": "612", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [37.359, 13.262]}, "s": {"a": 1, "k": [{"s": [100, 100, 0], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 0}, {"s": [100, 100, 0], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 12.255}, {"s": [120, 120, 109.091], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 19.404}, {"s": [100, 100, 0], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 24}]}, "p": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0.333}, "i": {"x": 0.667, "y": 0.667}, "s": [312.52, 167.677, 0], "t": 12.255}, {"s": [312.52, 167.677, 0], "t": 24}]}, "r": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [0], "t": 12.255}, {"s": [0], "t": 24}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "refId": "3", "ind": 3}, {"ty": 2, "nm": "left eyebrow", "sr": 1, "st": 0, "op": 24, "ip": 0, "ln": "610", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [25.155, 14.898]}, "s": {"a": 1, "k": [{"s": [100, 100, 100], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 12.255}, {"s": [120, 120, 109.091], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 19.404}, {"s": [100, 100, 100], "i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 24}]}, "p": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0.333}, "i": {"x": 0.667, "y": 0.667}, "s": [184.128, 168.573, 0], "t": 12.255}, {"s": [184.128, 168.573, 0], "t": 24}]}, "r": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [0], "t": 12.255}, {"s": [0], "t": 24}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "refId": "4", "ind": 4}, {"ty": 2, "nm": "Right eye", "sr": 1, "st": 0, "op": 24, "ip": 0, "ln": "608", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [32.695, 3.575]}, "s": {"a": 1, "k": [{"s": [100, 100, 100], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 0}, {"s": [115, 100, 100], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 5.617}, {"s": [100, 100, 100], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 12.255}, {"s": [100, 100, 100], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 24}]}, "p": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0.333}, "i": {"x": 0.667, "y": 0.667}, "s": [297.36, 219.033, 0], "t": 0}, {"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [297.36, 219.033, 0], "t": 12.255}, {"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [297.36, 205.033, 0], "t": 19.404}, {"s": [297.36, 219.033, 0], "t": 24}]}, "r": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [0], "t": 12.255}, {"s": [0], "t": 24}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "refId": "5", "ind": 5}, {"ty": 2, "nm": "Left eye", "sr": 1, "st": 0, "op": 24, "ip": 0, "ln": "606", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [32.63, 3.792]}, "s": {"a": 1, "k": [{"s": [100, 100, 100], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 0}, {"s": [115, 100, 100], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 5.617}, {"s": [100, 100, 100], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 12.255}, {"s": [100, 100, 100], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 24}]}, "p": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0.333}, "i": {"x": 0.667, "y": 0.667}, "s": [204.773, 219.136, 0], "t": 0}, {"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [204.773, 219.136, 0], "t": 12.255}, {"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [204.773, 205.136, 0], "t": 19.404}, {"s": [204.773, 219.136, 0], "t": 24}]}, "r": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [0], "t": 12.255}, {"s": [0], "t": 24}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "refId": "6", "ind": 6}, {"ty": 2, "nm": "Base", "sr": 1, "st": 0, "op": 24, "ip": 0, "ln": "604", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [170.5, 170.5]}, "s": {"a": 1, "k": [{"s": [100, 100, 100], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 12.255}, {"s": [100, 100, 100], "i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 24}]}, "p": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0.333}, "i": {"x": 0.667, "y": 0.667}, "s": [248.359, 249.878, 0], "t": 12.255}, {"s": [248.359, 249.878, 0], "t": 24}]}, "r": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [0], "t": 12.255}, {"s": [0], "t": 24}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "refId": "7", "ind": 7}], "v": "5.7.0", "fr": 24, "op": 24, "ip": 0, "assets": [{"id": "1", "e": 1, "w": 1, "h": 1, "p": "data:image/webp;base64,UklGRkgAAABXRUJQVlA4WAoAAAAQAAAAAwAAAwAAQUxQSAkAAAABBxAREYiI/gcAVlA4IBgAAAAwAQCdASoEAAQAAUAmJaQAA3AA/v02aAA=", "u": ""}, {"id": "2", "e": 1, "w": 247, "h": 148, "p": "data:image/webp;base64,UklGRlgGAABXRUJQVlA4WAoAAAAQAAAA9gAAkwAAQUxQSEcDAAABoBQBUCQwaglIQAIGUtUSRgISkNDPPHnmiQSqYgABSRYBe1CroP97sMecDHTv8YmICYD/uefeOW36cn2Xo9WOQSIK9ijs9d4zKmZLre/HAzrRewtGJVviftbv5VO99/zxgyYcUvzM/WLZK1d67798/2mz8sOQar/5vv0vMaARmvOx9ief2v25kEdRYYilPz7EfivkUToOKRbuY+6lEfa5JAqI0kAMlAv3ofdonJO1FKKA6BbmEIlSqf2V/J7zpZRERB4R3fQQkSiW0vrLyxyut/J32g14+AKD+/T3XEppfaIH22RGrOXu2ldJe7ic9R84deEe/FtmDoq2QGl8RMoqR0FZ6QiVRUdWWXgEyrInqqoYTmZVlTOkqnhmU1U4Y1WFZ4A1ZU4VRTGcJkWVc5ui6JxVlD8HTU/uQtITXPRqKlesmuIVaFryl5KW7KVNSQ0uGyWla5B1tN0QdGRusCoqcGfVULglaMjeYhXU4N6qn3iT14+7yainwd1ZO/G2TTvuNmi6aXB/1E18wOrGPgBZMxWe3DQTHoGmGPNM0EuGZw2rZXsISCsNnrZaoccgKcU+Z3WSYMCkEhzBaqTCkEkhfgyrjwaDJnXQKIaVwWYUIGUQDGtYF2Yc8KpIMHLVhB0KFZFg7KwGNoNZ1gLB6EEJbIaDqgOC8Z0KGryRNOBfYZr8CrwT5edeAlF6CV5bZcfmPU52Hl4cJFfg1Vlw9l2GxUbwcpRahdcHobn3QRZZgAmaKrACU3QsLrZzABTXBrP0woowzySqCjPNgmI7FVPlhDBXU6UUYLaGZZRgvo4lVGHGjuVTzZTAsXTYwaQdy4YdTNuxaBAm7lgwHqbuWCweJm+qUDxM31SReFigSQLxsMYkDg+r9LJgB+tEFgQ7WKmtYqgW1mqSEIqB5XqWQIQVu7o89rDouLjmYNnYVpYNLNzkZbGHxWNbU7WwfBNXRCBCV1ZTHEjR80o4gCAN8TKSAVnatIaCIE+b5tc8yNSmuTUPcrWJp9U8yNZQm1L1IOCtTCcjCNlSm0gjC5LGxHNIG8h7S+1t2RsQuqP6mpY2kL3ZYh2Oc3CgQoOU2yglegfKRE+lPsAl0+ZAsYiBKJbDTESE6OA/rQBWUDgg6gIAANAdAJ0BKvcAlAA+bTaWR6QjIiEoFTmogA2JaW7gwKP5ewwDMQPsAvtNDPxG7Sv77IgjyuQg/QuIvSBTKvHc+AFPShI8le9VS5AqdkxWqk3fGZddlEyQZ9m7CEN46Ja8WdhK9IpED8dAXim+vXdu3DkEGyB+QwtFgt11xxpMffuhkqxASuts0TlOt8wUEhi1syqYunFXJfEQDHoIpyLWJk1kl3VAgb9/IINujVUn3dzZw/EhnTgNTKNdVsfQC/LXQmLx32dXGuq2Ok32sxK9Kq411VN6J6z4flDn/ZhFbHSbtdalDz5aFGunUYOnvJac0n87FHE92QAA/UZLARv7Ml4gRmYxU1wQyNtqOsxotg9gRkF4nY9iAHf7UPkUHIF1QKhXx6pEp5oH4///Jntlj1A7uXwk26Df49FiUR+kJvET/Of8qhzlqNy2z0HW4yWeKFtI6fqBElQYk8uF4qD5dGAO0LKfDSVfjfyffp4VCopbrwf2PFJf23XZjRFaaUidWWwohxOsuQg1cJvF5SRT9GFW/5WFf5AtPr/8xuv0wnJrDyYJfZMOer54kYtHXJSqV8F6lJqfxY51sq26Z4cU6gXFdPM3yd1jry0DnfMrmGUkVXG3ImOfnDdrkqiPVyYlyp5PWWHQv5b9wP+FH6eJNY8yhNvVkuuf49lwDtrirED5T4CBMNQhw3/4WcVjm8Ky1b2Sh521QcImrWpmKm2DK5Sf7t+BKfonVwmedEiXfAe5XU3Alp6ecaZdNun0fUjWA/P8sOTIegEsKSMANJrlfR8qHMZTb/kBlQ15Mu/wSLMJRU+pO3Au/MtK+HkUFv9+38CxHwI/DYEYcqwNGgH80Ipta9VU4NO+hCGcVzo8Hy2pBoKLs7oCn0KiAd1n+rRO21mwnaWVDT53qWt38tYz62wUGR4ZGyEvJKJCDuG5wAAP1t20ANXy3tr4RnrfFQXZqQ0+p19n+5JXBv9dN8pKGbuyAyZHmyN34AAA", "u": ""}, {"id": "3", "e": 1, "w": 63, "h": 38, "p": "data:image/webp;base64,UklGRkoBAABXRUJQVlA4WAoAAAAQAAAAPgAAJQAAQUxQSOMAAAABBgi17VaVRkIkIKESkFAJkYCESkACEpCABCRUQhy8f2nSEXCIiAmgY9JysN45/Y04b/Xx23aVWLLUx+U9x5Fij651kRjz/ehuRQLow7Wq2/TwPpLT7AbFR6ofZ/JAltsNFg9Acjmc2MXl61xOD+7JDRDdrBuWAnzmzTphGgPQvQ9oFJiKdUHDAFp7oIFA7w7kSKD1mqVQUOwKJrGQ/QpnMJjrBbZoyHqBHA1ua7MpHHI0ccSDtQkdALUWkwFItYEyAnI2ICMgW0MeArZf6yCsP45R0NHQ0VB7wDoQ0/44ZaR/EgBWUDggQAAAAFAEAJ0BKj8AJgA+bSyTRqQiIaE1UggAgA2JaQAAFhAow/lv7NJuw3F7IAAA/voUvhiu/n430If/0xW+DMAAAAA=", "u": ""}, {"id": "4", "e": 1, "w": 65, "h": 39, "p": "data:image/webp;base64,UklGRlwBAABXRUJQVlA4WAoAAAAQAAAAQAAAJgAAQUxQSOUAAAABgFXbTl1rS4gEJCAhEpBQCZWAhEpAQiUgIRIqIQ72ua8CYdz/iJgAxMyl1l5PxZ7pvJ1/34dEk8M46i1FSs05taUo2jm/htDOpSbLUuNqk0XVuf5YosaIuuJiRCuYn42rrVUVLDycS++qWN248LkU66Vz+lMTIopxsreMmNk59zkEQbNzai8Im50zuyKuGCc+isjGcT8RunG8CUIXDj+K2OJDlyD4zUEviK4cNEH4PnAhvvL9gQ3bK8/Y0d94xo7Kl5aw5fXCBHvaXybYlH96wmaesZln7Pv88IKNC0nL2DpXxf8OAFZQOCBQAAAAcAQAnQEqQQAnAD5tLpJHJCIhoTESCSCADYlpATgAABqC/s+pV6IiT7OQvAAA/vtJ+73LWUZn1Wz1oq127/v/jsrTyO/9g/wFDbl9KAAAAAA=", "u": ""}, {"id": "5", "e": 1, "w": 65, "h": 25, "p": "data:image/webp;base64,UklGRjYBAABXRUJQVlA4WAoAAAAQAAAAQAAAGAAAQUxQSMoAAAABgFZbb9xIEATBEAxBEArBEAzBEAzBEAzBEALBEMTgm8XyNuvPiJgA2i8h5WbWFMXRzS6Wjo1ao7/D546DPbtTHDqONznBSXFlk22h49rCW1zDsraaPpfWltBlw0sx/yRhmmaJVWeAuBQx3QLTXik6gbJQMKnZ0cnQLZSpAlsT0+mgBspEhl2YLuRqIBkB5iN0aTDgBx5mpnu9jtqgj/RFN7tnAEdEAUP1dDc/AyGiOlBPt3Ox2if19AWlorGhnr7yC6hMX9s5+v8FVlA4IEYAAABwBACdASpBABkAPm0mkUWkIaGeaOQAQAbEtIAAC62YznCkkjPKuhPjB7R7AAD++l38Lhr/mo7VkC179/d8b/1/Ld0AAAAA", "u": ""}, {"id": "6", "e": 1, "w": 65, "h": 24, "p": "data:image/webp;base64,UklGRjYBAABXRUJQVlA4WAoAAAAQAAAAQAAAFwAAQUxQSMwAAAABgFXbbt1IEATBEAzBEAzBEALBEAxBEAJBEAQhEMzgtjN+z/MzIiaAVruQcr71851z8HQ0x6JYaJLcGZxubLTLbXOC7Zq2OMWRT1rGBcdaWBMeTFfVO+eiWqeAwgsyJu8cmEZDKjYGczMsGK0SaSlHqQOofowNg5ZoIyfrAWmEDX0NtDtYD3FA0X0CnXjVTvWdgm6mQ521ULnh0TZPx7K0oI3SEqaTpYX0SRtCh0vrGbjoeGnAEZEAT6QvKI1ARJwifc2r94X9XQv9FwJWUDggRAAAABAEAJ0BKkEAGAA+bS6TRqQioaEjTxiADYlpAAAYcgPHxyN8I6QPQhWAAP755LTwiVYPFe1f/+rzbMFKdaF1D/tkAAAA", "u": ""}, {"id": "7", "e": 1, "w": 341, "h": 341, "p": "data:image/webp;base64,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", "u": ""}]}