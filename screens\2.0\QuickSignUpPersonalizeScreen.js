import {
  Image,
  Modal,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  Alert,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import useSTheme from '../../theme/useSTheme';
import useSThemedStyles from '../../theme/useSThemedStyles';
import useDefaultStyle from '../../theme/useDefaultStyle';
import {_setAppThemeType} from '../../utils/AuthLogin';
import {AppStateContext} from '../../index';
import CustomStatusBar from '../../components/common/CustomStatusBar';
import LoginSignUpLinearGrad from '../../components/common/LoginSignUpLinearGrad';
import {ScrollView} from 'react-native';
import ModuleAppBar from '../../components/loginModule/ModuleAppBar';
import CustomProgressDialog from '../../components/common/CustomProgressDialog';
import EntutoTextView from '../../components/common/EntutoTextView';
import ModuleHeaderText from '../../components/loginModule/ModuleHeaderText';
import LoginModuleProgress from '../../components/loginModule/LoginModuleProgress';
import LoginModuleTitle from '../../components/loginModule/LoginModuleTitle';
import EntutoEditText from '../../components/common/EntutoEditText';
import PrimaryButton from '../../components/common/PrimaryButton';
import ThemeColorComponent from '../../components/ThemeColorComponent';
import SearchIcon from '../../assets/Images/icon/search_icon.png';
import SelectBoxComponent from '../../components/common/SelectBoxComponent';
import {CommonActions} from '@react-navigation/native';
import ServerConnector from '../../utils/ServerConnector';
import SuccessFailureMsgBox from '../../components/common/SuccessFailureMsgBox';
import OptionSelectionItem from '../../components/common/OptionSelectionItem';
import StyleSelector from '../../components/Login/ThemeToggle';
import {HARDCODED_EMOTIONS} from '../../data/MetaData';
import Sound from 'react-native-sound';

Sound.setCategory('Playback');

const QuickSignUpPersonalizeScreen = ({navigation}) => {
  const theme = useSTheme();
  const style = useSThemedStyles(styles);
  const {defaultStyle} = useDefaultStyle();

  const [selectedStyle, setSelectedStyle] = useState(
    theme.appThemeType === 'LIGHT' ? 'Light' : 'Dark',
  );
  const [showLoading, setShowLoading] = useState(false);
  const [errorMsg, setErrorMsg] = useState('');
  const [errorMsgType, setErrorMsgType] = useState('FAILED');
  const [refreshKey, setRefreshKey] = useState(Math.random());

  // Topic state variables
  const [topicList, setTopicList] = useState([]);
  const [selectedInterests, setSelectedInterests] = useState([]);
  const [displayTopicList, setDisplayTopicList] = useState([]);

  // Genre state variables
  const [genreList, setGenreList] = useState([]);
  const [selectedGenres, setSelectedGenres] = useState([]);
  const [displayGenreList, setDisplayGenreList] = useState([]);
  const [backupGenreList, setBackupGenreList] = useState([]);
  //Emotions
  const [selectedEmotions, setSelectedEmotions] = useState([]);
  const [displayEmotionList, setDisplayEmotionList] = useState([]);

  const [selectedUIColor, setSelectedUIColor] = useState('COLOR_1');

  // Modal state variables
  const [openInterestList, setOpenInterestList] = useState(false);
  const [openGenreList, setOpenGenreList] = useState(false);
  const [openEmotionList, setOpenEmotionList] = useState(false);

  const {selectedGuide, fullUserDetails} = React.useContext(AppStateContext);

  useEffect(() => {
    getGenreTopicMasters();
    setDisplayEmotionList(HARDCODED_EMOTIONS);
  }, []);
  // Fisher-Yates shuffle
  function shuffleArray(array) {
    const arr = [...array];
    for (let i = arr.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [arr[i], arr[j]] = [arr[j], arr[i]];
    }
    return arr;
  }

  const getGenreTopicMasters = () => {
    setShowLoading(true);
    let hashMap = {
      _action_code: '11:GET_GENRE_TOPIC_MASTERS',
    };
    let connector = new ServerConnector();
    connector.postData(
      hashMap,
      data => {
        // success method
        setShowLoading(false);
        let topicTempList = [];
        let genreTempList = [];
        if (data.data.topic_masters) {
          Object.values(data.data.topic_masters).forEach(item => {
            topicTempList.push({
              label: item.label,
              value: item.value,
            });
          });
        }

        if (data.data.genre_masters) {
          Object.values(data.data.genre_masters).forEach(item => {
            genreTempList.push({
              label: item.label,
              value: item.value,
            });
          });
        }

        // Shuffle the lists before setting state
        const shuffledTopics = shuffleArray(topicTempList);
        const shuffledGenres = shuffleArray(genreTempList);

        setTopicList(shuffledTopics);
        setDisplayTopicList(shuffledTopics);
        setGenreList(shuffledGenres);
        setDisplayGenreList(shuffledGenres);
      },
      (errorCode, errorMessage, data) => {
        // failure method
        setShowLoading(false);
        setErrorMsg(errorMessage);
        setErrorMsgType('FAILED');
        setRefreshKey(Math.random());
      },
    );
  };

  // Removed getUserGenreListService function

  const letsGetStartedBtnClick = () => {
    if (formValid()) {
      setShowLoading(true);
      updateUserInterestService();
    }
  };
  const searchEmotionBtnPress = () => {
    setOpenEmotionList(true);
  };

  const formValid = () => {
    let isValid = true;
    if (selectedInterests.length < 9) {
      setErrorMsg(
        'Please select minimum of 9 interesting topics, for the best experience.',
      );
      setRefreshKey(Math.random());
      isValid = false;
    } else if (selectedGenres.length < 9) {
      setErrorMsg(
        'Please select minimum of 9 genres, for the best experience.',
      );
      setRefreshKey(Math.random());
      isValid = false;
    } else if (selectedEmotions.length < 9) {
      setErrorMsg(
        'Please select minimum of 9 emotions, for the best experience.',
      );
      setRefreshKey(Math.random());
      isValid = false;
    }
    return isValid;
  };

  const themeColorChange = themeValue => {
    setSelectedUIColor(themeValue);
  };

  const handleStyleChange = style => {
    setSelectedStyle(style);
    // Convert the style value to the format expected by the theme system
    const themeType = style === 'Light' ? 'LIGHT' : 'DARK';
    // Update the global theme state
    theme.changeAppTheme(themeType);
    // Persist the theme change in AsyncStorage
    _setAppThemeType(themeType);
  };

  const selectInterestBoxClick = (clickID, obj) => {
    setOpenInterestList(false);
    if (clickID == 'DONE') {
      setSelectedInterests([...[], ...obj.selectedItem]);
      createUpdatedList(
        obj.selectedItem,
        topicList,
        topicList.map(item => item.value),
      );
    }
  };

  const selectGenreBoxClick = (clickID, obj) => {
    setOpenGenreList(false);
    if (clickID == 'DONE') {
      setSelectedGenres([...[], ...obj.selectedItem]);
      createUpdatedGenreList(
        obj.selectedItem,
        genreList,
        genreList.map(item => item.value),
      );
    }
  };

  const createUpdatedList = (selectedItems, tempList, tempBackUplList) => {
    let tempTopicList = JSON.parse(JSON.stringify(tempList));
    let list = [];

    // First add all selected items
    tempTopicList.forEach((item, index) => {
      if (selectedItems.includes(item.value)) {
        item.isChecked = true;
        list.push(item);
      }
    });

    // Then add non-selected items until we reach max 9
    if (list.length < 9) {
      tempTopicList.forEach(item => {
        if (!selectedItems.includes(item.value) && list.length < 9) {
          item.isChecked = false;
          list.push(item);
        }
      });
    }

    setDisplayTopicList(list);
  };

  const createUpdatedGenreList = (selectedItems, tempList, tempBackUpList) => {
    let tempGenreList = JSON.parse(JSON.stringify(tempList));
    let list = [];

    // First add all selected items
    tempGenreList.forEach((item, index) => {
      if (selectedItems.includes(item.value)) {
        item.isChecked = true;
        list.push(item);
      }
    });

    // Then add non-selected items until we reach max 6
    if (list.length < 9) {
      tempGenreList.forEach(item => {
        if (!selectedItems.includes(item.value) && list.length < 9) {
          item.isChecked = false;
          list.push(item);
        }
      });
    }

    setDisplayGenreList(list);
  };

  const onInterestItemPress = (clickID, obj) => {
    if (clickID == 'ITEM_CLICK') {
      let tempTopicList = JSON.parse(JSON.stringify(displayTopicList));
      let list = [...tempTopicList];
      list[obj.index].isChecked = !list[obj.index].isChecked;
      if (list[obj.index].isChecked) {
        setSelectedInterests([...selectedInterests, obj.value]);
      } else {
        setSelectedInterests(
          selectedInterests.filter(item => item !== obj.value),
        );
      }
      setDisplayTopicList(list);
    }
  };

  const onGenreItemPress = (clickID, obj) => {
    if (clickID == 'ITEM_CLICK') {
      let tempGenreList = JSON.parse(JSON.stringify(displayGenreList));
      let list = [...tempGenreList];
      list[obj.index].isChecked = !list[obj.index].isChecked;
      if (list[obj.index].isChecked) {
        setSelectedGenres([...selectedGenres, obj.value]);
      } else {
        setSelectedGenres(selectedGenres.filter(item => item !== obj.value));
      }
      setDisplayGenreList(list);
    }
  };
  const onEmotionItemPress = (clickID, obj) => {
    if (clickID === 'ITEM_CLICK') {
      let tempEmotionList = JSON.parse(JSON.stringify(displayEmotionList));
      let list = [...tempEmotionList];
      list[obj.index].isChecked = !list[obj.index].isChecked;

      if (list[obj.index].isChecked) {
        setSelectedEmotions([...selectedEmotions, obj.value]);
      } else {
        setSelectedEmotions(
          selectedEmotions.filter(item => item !== obj.value),
        );
      }

      setDisplayEmotionList(list);
    }
  };
  const updateUserInterestService = () => {
    let hashMap = {
      _action_code: '11:UPDATE_USER_INTERESTS',
      interests: JSON.stringify(selectedInterests),
      genres: JSON.stringify(selectedGenres),
      _user_seq: fullUserDetails._user_seq,
      user_id: fullUserDetails._user_seq,
      password: fullUserDetails._password,
    };
    let connector = new ServerConnector();
    connector.postData(
      hashMap,
      data => {
        // success method
        setShowLoading(true);
        navigation.navigate('NotificationGuideSelectionScreen');
      },
      (errorCode, errorMessage, data) => {
        // failure method
        setShowLoading(false);
        var fieldErrorShown = false;
        if (errorCode === 'E006') {
          if (data && data != null && data.data) {
            if (data.data.interests) {
              setErrorMsg(data.data.interests);
              setErrorMsgType('FAILED');
              setRefreshKey(Math.random());
              fieldErrorShown = true;
            } else if (data.data.genres) {
              setErrorMsg(data.data.genres);
              setErrorMsgType('FAILED');
              setRefreshKey(Math.random());
              fieldErrorShown = true;
            }
          }
        }
        if (!fieldErrorShown) {
          setErrorMsg(errorMessage);
          setErrorMsgType('FAILED');
          setRefreshKey(Math.random());
        }
      },
    );
  };
  const selectEmotionBoxClick = (clickID, obj) => {
    setOpenEmotionList(false);
    if (clickID == 'DONE') {
      setSelectedEmotions([...[], ...obj.selectedItem]);
      // Create updated list of emotions
      let tempEmotionList = JSON.parse(JSON.stringify(displayEmotionList));
      tempEmotionList.forEach(item => {
        // Fix: Check against item.value instead of item.label to match the SelectBoxComponent logic
        item.isChecked = obj.selectedItem.includes(item.value);
      });
      setDisplayEmotionList(tempEmotionList);
    }
  };
  const updateUserColorService = () => {
    let hashMap = {
      _action_code: '11:UPDATE_USER_UI_COLOUR',
      ui_colour: selectedUIColor,
    };
    let connector = new ServerConnector();
    connector.postData(
      hashMap,
      data => {
        // success method
        setShowLoading(false);
        theme.changeThemeColor(selectedUIColor);
        navigation.dispatch(
          CommonActions.reset({
            index: 1,
            routes: [
              {
                name: 'VideoContentScreen',
                params: {
                  postSeq: -1,
                  postProfileSeq: -1,
                  cameFrom: 'PERSONALIZATION',
                },
              },
            ],
          }),
        );
      },
      (errorCode, errorMessage, data) => {
        // failure method
        setShowLoading(false);
        var fieldErrorShown = false;
        if (errorCode === 'E006') {
          if (data && data != null && data.data) {
            if (data.data.ui_colour) {
              setErrorMsg(data.data.ui_colour);
              setErrorMsgType('FAILED');
              setRefreshKey(Math.random());
              fieldErrorShown = true;
            }
          }
        }
        if (!fieldErrorShown) {
          setErrorMsg(errorMessage);
          setErrorMsgType('FAILED');
          setRefreshKey(Math.random());
        }
      },
    );
  };

  const searchTopicBtnPress = () => {
    setOpenInterestList(true);
  };

  const searchGenreBtnPress = () => {
    setOpenGenreList(true);
  };

  return (
    <>
      <CustomStatusBar translucent={false} hidden={false} />
      <CustomProgressDialog showLoading={showLoading} />
      <View style={{flex: 1, position: 'relative'}}>
        <LoginSignUpLinearGrad />
        <ModuleAppBar navigation={navigation} />
        <ScrollView keyboardShouldPersistTaps="handled">
          <View style={defaultStyle.loginModuleContainer}>
            <LoginModuleTitle
              firstTitleText={`Let's Personalize Your`}
              secondTitleText="Digital Experience!"
              style={{marginTop: 50}}
            />
            <View style={defaultStyle.loginModuleFormContainer}>
              <StyleSelector
                selectedStyle={selectedStyle}
                onStyleChange={handleStyleChange}
              />
              {/* Genre */}
              <View>
                <EntutoTextView style={style.labelText}>
                  What genres of content are you interested in?
                </EntutoTextView>
              </View>
              <EntutoTextView style={style.warringInterestText}>
                Select at least 9
              </EntutoTextView>

              <View
                style={{
                  marginTop: theme.dimensions.loginModuleInputMT,
                  flexDirection: 'row',
                  flexWrap: 'wrap',
                }}>
                {displayGenreList.slice(0, 9).map((obj, i) => {
                  return (
                    <OptionSelectionItem
                      key={i}
                      index={i}
                      label={obj.label}
                      isChecked={obj.isChecked}
                      value={obj.value}
                      onItemSelected={onGenreItemPress}
                    />
                  );
                })}

                <OptionSelectionItem
                  isSearchIcon={true}
                  inactiveImageValue={SearchIcon}
                  activeImageValue={SearchIcon}
                  onItemSelected={searchGenreBtnPress}
                  showImage={true}
                />
              </View>

              {/* Topics */}
              <View
                style={{marginTop: theme.dimensions.loginModuleInputMT * 1.5}}>
                <EntutoTextView style={style.labelText}>
                  Why don't you select some topics as well?
                </EntutoTextView>
              </View>

              <EntutoTextView style={style.warringInterestText}>
                Select at least 9
              </EntutoTextView>

              <View
                style={{
                  marginTop: theme.dimensions.loginModuleInputMT,
                  flexDirection: 'row',
                  flexWrap: 'wrap',
                }}>
                {displayTopicList.slice(0, 9).map((obj, i) => {
                  return (
                    <OptionSelectionItem
                      key={i}
                      index={i}
                      label={obj.label}
                      isChecked={obj.isChecked}
                      value={obj.value}
                      onItemSelected={onInterestItemPress}
                    />
                  );
                })}

                <OptionSelectionItem
                  isSearchIcon={true}
                  inactiveImageValue={SearchIcon}
                  activeImageValue={SearchIcon}
                  onItemSelected={searchTopicBtnPress}
                  showImage={true}
                />
              </View>
              {/* Emotions  */}
              <View
                style={{marginTop: theme.dimensions.loginModuleInputMT * 1.5}}>
                <EntutoTextView style={style.labelText}>
                  Select the emotions you want to experience
                </EntutoTextView>
              </View>

              <EntutoTextView style={style.warringInterestText}>
                Select at least 9
              </EntutoTextView>
              <View style={{marginTop: theme.dimensions.loginModuleInputMT}}>
                {Array.from({
                  length: Math.ceil(displayEmotionList.length / 2),
                }).map((_, rowIdx) => (
                  <View
                    key={rowIdx}
                    style={{
                      flexDirection: 'row',
                      flexWrap: 'nowrap',
                      width: '100%',
                    }}>
                    {displayEmotionList
                      .slice(rowIdx * 2, rowIdx * 2 + 2)
                      .map((obj, i) => (
                        <View key={rowIdx * 2 + i} style={{flex: 1}}>
                          <OptionSelectionItem
                            index={rowIdx * 2 + i}
                            label={obj.label}
                            isChecked={obj.isChecked}
                            value={obj.value}
                            onItemSelected={onEmotionItemPress}
                            inactiveImageValue={obj.inactivePath}
                            activeImageValue={obj.activePath}
                            showImage={true}
                            lottieFile={obj.lottiePath}
                            isAnimated={true}
                            soundFileName={obj.audioPath}
                            vibrationPattern={obj.vibrationPattern}
                          />
                        </View>
                      ))}
                    {/* Add search icon only in the last row and after the last emotion item */}
                    {rowIdx ===
                      Math.floor((displayEmotionList.length - 1) / 2) &&
                    displayEmotionList.length % 2 !== 0 ? (
                      <View style={{flex: 1}}>
                        <OptionSelectionItem
                          isSearchIcon={true}
                          inactiveImageValue={SearchIcon}
                          activeImageValue={SearchIcon}
                          onItemSelected={searchEmotionBtnPress}
                          showImage={true}
                          isAnimated={true}
                        />
                      </View>
                    ) : null}
                  </View>
                ))}
                {/* If the number of emotions is even, add the search icon in a new row */}
                {displayEmotionList.length % 2 === 0 && (
                  <View style={{flexDirection: 'row', width: '100%'}}>
                    <View style={{flex: 1}}>
                      <OptionSelectionItem
                        isSearchIcon={true}
                        inactiveImageValue={SearchIcon}
                        activeImageValue={SearchIcon}
                        onItemSelected={searchEmotionBtnPress}
                        showImage={true}
                        isAnimated={true}
                      />
                    </View>
                    <View style={{flex: 1}} />
                  </View>
                )}
              </View>
              <View style={{marginTop: 54}}>
                <PrimaryButton
                  label="Keep Going"
                  style={{}}
                  uppercase={false}
                  onPress={() => letsGetStartedBtnClick()}
                />
              </View>
            </View>
          </View>
        </ScrollView>
      </View>
      {/* Interest Selection Modal */}
      <Modal
        animationType="fade"
        visible={openInterestList}
        style={{margin: 0, flex: 1}}>
        <SelectBoxComponent
          selectBoxClick={selectInterestBoxClick}
          list={JSON.parse(JSON.stringify(topicList))}
          selectedValue={selectedInterests}
          title="Select Interest"
          maxSelectedValue={topicList.length}
          multiSelect={true}
          labelField="label"
          valueField="value"
        />
      </Modal>
      {/* Emotion Selection Modal */}
      <Modal
        animationType="fade"
        visible={openEmotionList}
        style={{margin: 0, flex: 1}}>
        <SelectBoxComponent
          selectBoxClick={selectEmotionBoxClick}
          list={JSON.parse(JSON.stringify(displayEmotionList))}
          selectedValue={selectedEmotions}
          title="Select Emotions"
          maxSelectedValue={displayEmotionList.length}
          multiSelect={true}
          isEmotions={true} // Set this to true for emotions
          isAnimated={true} // Enable animations for emotions
        />
      </Modal>
      {/* Genre Selection Modal */}
      <Modal
        animationType="fade"
        visible={openGenreList}
        style={{margin: 0, flex: 1}}>
        <SelectBoxComponent
          selectBoxClick={selectGenreBoxClick}
          list={JSON.parse(JSON.stringify(genreList))}
          selectedValue={selectedGenres}
          title="Select Genre"
          maxSelectedValue={genreList.length}
          multiSelect={true}
          labelField="label"
          valueField="value"
        />
      </Modal>

      {errorMsg.length != 0 ? (
        <SuccessFailureMsgBox alertMsg={errorMsg} alertKey={refreshKey} />
      ) : null}
    </>
  );
};

export default QuickSignUpPersonalizeScreen;

const styles = theme =>
  StyleSheet.create({
    labelText: {
      fontSize: theme.calculateFontSize(theme.dimensions.personalizeLabelText),
    },
    itemContainer: {
      padding: theme.dimensions.loginModuleInputPadding,
      borderWidth: 1,
      justifyContent: 'center',
      alignItems: 'center',
      height: 36,
      // borderRadius: 36 / 2,
      marginEnd: 8,
      marginBottom: 8,
      minWidth: 90,
    },
    itemContainerText: {
      paddingHorizontal: 25,
    },
    searchIcon: {
      width: 17,
      height: 17,
      resizeMode: 'contain',
      tintColor: '#707070',
      paddingHorizontal: 25,
    },
    warringInterestText: {
      color: theme.colors.primaryColor,
      marginTop: 10,
    },
  });
