import React from 'react';
import useOrientationManager from '../../hooks/useOrientationManager';

/**
 * Higher-order component that wraps screens with orientation management
 * @param {React.Component} WrappedComponent - The screen component to wrap
 * @param {boolean} allowRotation - Whether to allow rotation for this screen
 * @returns {React.Component} - Wrapped component with orientation management
 */
const withOrientationLock = (WrappedComponent, allowRotation = false) => {
  function OrientationWrapper(props) {
    // Use the orientation manager hook
    useOrientationManager(allowRotation);

    // Render the wrapped component with all props
    return React.createElement(WrappedComponent, props);
  }

  // Set display name for debugging
  OrientationWrapper.displayName = `withOrientationLock(${
    WrappedComponent.displayName || WrappedComponent.name || 'Component'
  })`;

  return OrientationWrapper;
};

export default withOrientationLock;
