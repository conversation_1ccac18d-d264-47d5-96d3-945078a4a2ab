{"nm": "Hmm", "h": 269, "w": 296, "meta": {"g": "@lottiefiles/toolkit-js 0.65.0", "tc": "#ffffff"}, "layers": [{"ty": 6, "nm": "hmmm#11.wav", "sr": 1, "st": 0, "op": 230, "ip": 0, "ln": "1018", "au": {"lv": {"a": 0, "k": [0, 0]}}, "refId": "1", "ind": 1}, {"ty": 2, "nm": "H ", "sr": 1, "st": 0, "op": 24, "ip": 0, "ln": "862", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [22, 26]}, "s": {"a": 1, "k": [{"s": [100, 100, 100], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 2.126}, {"s": [100, 115, 0], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 5.379}, {"s": [100, 100, 100], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 8.476}, {"s": [100, 115, 100], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 11.577}, {"s": [100, 100, 100], "i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 14.676}]}, "p": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [49.826, 135.077, 0], "t": 0}, {"o": {"x": 0.333, "y": 0.333}, "i": {"x": 0.667, "y": 0.667}, "s": [82, 135.077, 0], "t": 17}, {"o": {"x": 0.333, "y": 0.333}, "i": {"x": 0.667, "y": 0.667}, "s": [82, 135.077, 0], "t": 19}, {"s": [82, 135.077, 0], "t": 24}]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [100], "t": 0}, {"s": [100], "t": 17}]}}, "refId": "2", "ind": 2}, {"ty": 2, "nm": "m1", "sr": 1, "st": 0, "op": 24, "ip": 0, "ln": "860", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [25.5, 20.5]}, "s": {"a": 1, "k": [{"s": [100, 100, 100], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 1.594}, {"s": [100, 115, 0], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 4.848}, {"s": [100, 100, 100], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 7.845}, {"s": [100, 115, 100], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 10.646}, {"s": [100, 100, 100], "i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 13.547}]}, "p": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [101.326, 139.577, 0], "t": 0}, {"o": {"x": 0.333, "y": 0.333}, "i": {"x": 0.667, "y": 0.667}, "s": [133.5, 139.577, 0], "t": 17}, {"o": {"x": 0.333, "y": 0.333}, "i": {"x": 0.667, "y": 0.667}, "s": [133.5, 139.577, 0], "t": 19}, {"s": [133.5, 139.577, 0], "t": 24}]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [100], "t": 0}, {"s": [100], "t": 17}]}}, "refId": "3", "ind": 3}, {"ty": 2, "nm": "m2", "sr": 1, "st": 0, "op": 24, "ip": 0, "ln": "858", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [25.5, 16.5]}, "s": {"a": 1, "k": [{"s": [100, 100, 100], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 1.062}, {"s": [100, 115, 0], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 4.316}, {"s": [100, 100, 100], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 7.393}, {"s": [100, 115, 100], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 10.47}, {"s": [100, 100, 100], "i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 13.547}]}, "p": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [156.326, 130.577, 0], "t": 0}, {"o": {"x": 0.333, "y": 0.333}, "i": {"x": 0.667, "y": 0.667}, "s": [188.5, 130.577, 0], "t": 17}, {"o": {"x": 0.333, "y": 0.333}, "i": {"x": 0.667, "y": 0.667}, "s": [188.5, 130.577, 0], "t": 19}, {"s": [188.5, 130.577, 0], "t": 24}]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [100], "t": 0}, {"s": [100], "t": 17}]}}, "refId": "4", "ind": 4}, {"ty": 2, "nm": "period", "sr": 1, "st": 0, "op": 24, "ip": 0, "ln": "856", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [6.5, 6.5]}, "s": {"a": 1, "k": [{"s": [100, 100, 100], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 1.062}, {"s": [100, 115, 0], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 3.785}, {"s": [100, 100, 100], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 6.663}, {"s": [100, 115, 100], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 9.54}, {"s": [100, 100, 100], "i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 12.418}]}, "p": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [192.326, 141.577, 0], "t": 0}, {"o": {"x": 0.333, "y": 0.333}, "i": {"x": 0.667, "y": 0.667}, "s": [224.5, 141.577, 0], "t": 17}, {"o": {"x": 0.333, "y": 0.333}, "i": {"x": 0.667, "y": 0.667}, "s": [224.5, 141.577, 0], "t": 19}, {"s": [224.5, 141.577, 0], "t": 24}]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [100], "t": 0}, {"s": [100], "t": 17}]}}, "refId": "5", "ind": 5}, {"ty": 2, "nm": "backgrounf", "sr": 1, "st": -30, "op": 24, "ip": 19, "ln": "854", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [85.5, 26]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [149.5, 135.077, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [0], "t": 19}, {"s": [100], "t": 23.999}]}}, "refId": "6", "ind": 6}], "v": "5.7.0", "fr": 24, "op": 24, "ip": 0, "assets": [{"id": "1", "e": 0, "p": "/Users/<USER>/Desktop/Brand Jar/so true/Sotrue_all_1s/Sotrue_text_1s/(Footage)/hmmm#11.wav", "u": ""}, {"id": "2", "e": 1, "w": 44, "h": 52, "p": "data:image/webp;base64,UklGRuwBAABXRUJQVlA4WAoAAAAQAAAAKwAAMwAAQUxQSMAAAAABgFZtT9jqk4AEJCAhEpCABCREQiUgAQmRMAlIwEG2e1tSsvV/REwAUAocs84jOlJVKcGRqvYSHKmqcHSkyk9BnInSbdnCuswm+iXNDwGyFlzhkfBNYpKvUjcvV2KarSQ/p3JUCm7OX9KYx1LfcSMD0RU9X3VFQHXFa60PRwQEqocML5eRiLmZ+l2n2SRbeC2awhaYsGu60kfqlrRHLPSz0lgapmEBcptXYlIbgHwMRwBS7dPP/5QttMXuK9BlwilWUDggBgEAALAIAJ0BKiwANAA+YSaORaQiIRlXMEAGBLUAX7YopqP4Rjpxj+4C5Tf0l9lX/AfwDWVfQA/QD0tPYe/bv0pQV4oN6mwlOnRHRd6FShyMoAD+9Q5f/zvMgEX/eL/MmTPaFYT/2PdoG9w23oVP/5k3//0KLmaO/DT/HKUpaYFf+tqf8KEgXNtsyrBe65NUDBLeXyTGZpyESdHBP5lgPm4fyww5Et49Y4Fg9fjFu1t/LHdTvjxpS5tKKnUuuvqa1JBuCfT/95IHiR52zsC7FWnE/+X+lz/H+KDu/qVxXtNgtt/9zvH/WfOnxZ/3Dc3rBVyeMdXc/48r//0KLrc/pUJRFfSI/6EYAAA=", "u": ""}, {"id": "3", "e": 1, "w": 51, "h": 41, "p": "data:image/webp;base64,UklGRkQCAABXRUJQVlA4WAoAAAAQAAAAMgAAKAAAQUxQSPYAAAABgFDbTh3rSkACEpAQCUhAAhIioRKQUAmRUAlIwEHehwmBjoCImAB878TSVfVp8ZTAXY3ljDzUnr0iZS4UJk1Xu0u8us5bAJqup7Uoan1CUUeaRcrcZABlqF2GT5JHP6PoiQRS45P1vD5e4D0uHjYc8AQgjbN6AAA5q+A/72GqC2FNgGwbCQCbBC5sqwCQTDxrGzqmpjqTDexADm2FjuCV8AZMyYT5s3BbxgZdEIucUd5AX4MdaGVYrmPUyg6yNITDKfnfuGvC1ON2YJWaYPQQhwg7nbG6RZjCvjxT1XFXgr+NZndN2Fs89vOHLlzCOV24EI6OeCFWUDggKAEAAHAKAJ0BKjMAKQA+bS6RRqQioaEqqwCADYlsAMAf2ojvqP5FcLREGQufmfuA9jPnANwB+un6V+y7+gHUzda76AH6m+jl+qvwC/tp6QoGY7Y4J+zwKd6dXLdBC80AAP7zwgvYngWR++040J8yvynDi3JTCyG0sH1VsFZwyfJ+nD+YhcvbzQODqOLmI/qKTT/2zzL/4vdHu+rWnkaryCbnbpxoDgfT7FdFCdJnwZIOqvFbaZrTf+D9A9mCDGzo8lmG6m0ccgQer/quR81RcFv8swmcnmz+/3YzCaCf/W/n/tuEtgqY22/LowtFJikEuNm61ZTVDELRzmJFR/5Bc9s3NE9sPUwT9AsLINxpHu0cKHPVXhvU/haf/eZT86puZMcZIr0M1UgAAAAA", "u": ""}, {"id": "4", "e": 1, "w": 51, "h": 33, "p": "data:image/webp;base64,UklGRhgCAABXRUJQVlA4WAoAAAAQAAAAMgAAIAAAQUxQSOAAAAABgFDbThjpS0ACEpAQCUhAAhIiYSRUQiVEAhKQEAeZ3W1DaFdAREwA/sf6kWlTzp5uiP/WHFGoAYnV7pUBJFa7nZzvqPIhamaCouY/kIa5lS/InFLUVnnYqqSVqfbAc+Wh7Q3jDZbQtyn3Y4HAC0K5L8wEoG05AYB9FX+nqyzM9IdcA5fiwkLHGu8rATVGXAigAFmYEWmbROBFGndccYR5GVAPB2iQ7ZEXlX0SQC5CdvWnkIvi6r4ZwPtsW40acjJzL1eZiBrzR4ZZBkdUws4Mj8onX+3/o8I148HtaBlPB1ZQOCASAQAAUAgAnQEqMwAhAD5tMJJHJCKhoSVtmIANiWgAyYBap0/FW++/ANwB+tf6V+8rzoHW5egB5SP67fA3+33o7gM9j3bKAxxhol1I+cggAP7zwmf/mId+HzZzDy+7du1K/b8yv9uFNe2/6L/VOFyd3hEk3BXi9tNJm5qt4iqgzn7Zblh1G91Np19gd7KZvl0GUczkTvdn3+TudUORCf3i4OnhAqDsWGKMP3j6zi43cQPAukdkf23+7LOq5cOu9qn5WbXmhXs7TN/Jfmg6KP7sw38rwT7kKwsN6LHOSUzGA9vuwcp9ucuDB9XgcCWVXozaOGLwI+1TsEOsf+2su//xi/+PGvQPu3/x416CtmTARW4AAAAAAA==", "u": ""}, {"id": "5", "e": 1, "w": 13, "h": 13, "p": "data:image/webp;base64,UklGRroAAABXRUJQVlA4WAoAAAAQAAAADAAADAAAQUxQSEwAAAABf0CQbTODDeH+oGeIiKS7BKPatpVcbE4EalCFmeegCQ0YWQyiuNv3T4WI/k9AbkFc+kBCSOlISO9LCPnR4xA6VfVK4LUSILwLAW4GVlA4IEgAAADQAQCdASoNAA0AAUAmJbACdH8D2DgiYAD+90av/tA+7mKyDsH8f/ujXaSj/6QDxKn/18v9ohBJE9nSKfnn/sUu/obqxfZAAAA=", "u": ""}, {"id": "6", "e": 1, "w": 171, "h": 52, "p": "data:image/webp;base64,UklGRt4CAABXRUJQVlA4WAoAAAAQAAAAqgAAMwAAQUxQSHwCAAABcBQASBgRwQhGMAIRjGCERSCCERZhEYhgBCPYgMccQ9zdOyImACCdEf5mFLnyvxRFRDrFvwhuItKO9D+JSD/znxM0IvLnoK7/kETcZDBTeoiFiOhAP/wm0oGI4VsilgiAXZQcAABZHseJe6Dod4t4EDcREQyn6EeEKnqOOwQdbyba3ORtq/J2JA/1DXzUKQ5HdEBfEY+TRaRfxcYn+0ubIIuSwy6S3KGOvEQWPW9D68ouiAdhmuCQt+ilFhrOcIdwsMwbAiR5Tya9BGwvMgCksYx2oyHaHIYBW4wIAEnHcKfvQBUiVe4IqYl+sJglXYZ7V8UJfk7lIY+YhjicoarDlDUdDMonsChzF49kUA3YAtfFN3lVl02KAW1xEepIFw2sma5VOGsGtOzS3dtJmEzAyUgAcC1KM1kEpvxu3vkiYpO2qgAAZBVOSAUGeTPLB1nc4I7u8Ddwzk1X9ro+o9kQACRddIQWrOFZyWELNmlwZ80AgzA5NeyKRa4jPo2t8B1bwJQ3EhEuYSI7ddgqq9JMzETkyjug6jSos+KCVGBwzsaTCL3h2hZcFmRAMzLo7mgmWoSkIwBIhbiZkEVxwTNxkZbhu3lAPIiW4SbXjEWkMxMR4SwgIhIR8/A199K80SxjhJXxc9JMtDjjd0HRLpz5jLUpAA53pAJHeONaEvgP+ewP9CPSRQgbp9rXxU+gyQfmcxU6SRby3Gr6ivvlrhqgWSMM8K2sQw+saStazQG+11E24AURvnlN0aHdoYqzrx5LyAk9tFoifHtzN4xazQF+YDyaLzE4KAf4nfFgo8vJ7w3l7Aa8hOuRftU9luiDT8IIf+cwCfCn4nOAvxBWUDggPAAAABAFAJ0BKqsANAA+bTaZSaQjIqEgyACADYlpAAAGN7qb6NqHOMoB7qb6NqHOMoB7pgAA/vucwAAAAAAAAA==", "u": ""}]}