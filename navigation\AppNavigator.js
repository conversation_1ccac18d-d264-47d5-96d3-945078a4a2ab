import React, {useContext, useEffect, useState} from 'react';
import {
  StatusBar,
  StyleSheet,
  Linking,
  Alert,
  Platform,
  AppState,
  Appearance,
} from 'react-native';
import {
  DefaultTheme,
  useNavigation,
  CommonActions,
} from '@react-navigation/native';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import SplashScreen from '../screens/SplashScreen';
import LoginScreen from '../screens/LoginScreen';
import Colors from '../constants/Colors';
import {SafeAreaProvider} from 'react-native-safe-area-context';
import ForgotPasswordScreen from '../screens/ForgotPasswordScreen';
import SignupScreen from '../screens/SignupScreen';
import {AppStateContext, PageRefreshContext} from '..';
import HomeDrawerNavigator from './HomeDrawerNavigator';
import ForgotVerifyEmailScreen from '../screens/ForgotVerifyEmailScreen';
import ForgotResetPassScreen from '../screens/ForgotResetPassScreen';
import ForgotResetPassSuccessScreen from '../screens/ForgotResetPassSuccessScreen';
import SignupOtpScreen from '../screens/SignupOtpScreen';
import StoryStatusScreen from '../screens/StoryStatusScreen';
import VideoDisplayScreen from '../screens/VideoDisplayScreen';
import OthersProfileScreen from '../screens/OthersProfileScreen';
import AccountSettingsScreen from '../screens/AccountSettingsScreen';
import FollowingScreen from '../screens/FollowingScreen';
import CommentScreen from '../screens/CommentScreen';
import CameraScreen from '../screens/CameraScreen';
import CustomStatusBar from '../components/common/CustomStatusBar';
import {BottomNavigator} from './BottomNavigator';
import SearchResultScreen from '../screens/SearchResultScreen';
import EditProfileScreen from '../screens/EditProfileScreen';
import AccountInfoScreen from '../screens/AccountInfoScreen';
import BlockedAccountScreen from '../screens/BlockedAccountScreen';
import PrivacySafetyScreen from '../screens/PrivacySafetyScreen';
import CaptureMediaDisplayScreen from '../screens/CaptureMediaDisplayScreen';
import AddStoryScreen from '../screens/AddStoryScreen';
import SubscriptionScreen from '../screens/SubscriptionScreen';
import MySubscribersScreen from '../screens/MySubscribersScreen';
import VerificationsScreen from '../screens/VerificationsScreen';
import SuccessfullVerificationScreen from '../screens/SuccessfullVerificationScreen';
import ReferAndEarnScreen from '../screens/ReferAndEarnScreen';
import ReferralHistoryScreen from '../screens/ReferralHistoryScreen';
import MySubscriptionsScreen from '../screens/MySubscriptionsScreen';
import AddPostScreen from '../screens/AddPostScreen';
import RestrictedAccountScreen from '../screens/RestrictedAccountScreen';
import FollowersScreen from '../screens/FollowersScreen';
import BookmarkListScreen from '../screens/BookmarkListScreen';
import NotificationSettingsScreen from '../screens/NotificationSettingsScreen';
import SinglePostScreen from '../screens/SinglePostScreen';
import SelectDataFieldScreen from '../screens/SelectDataFieldScreen';
import ImageDisplayScreen from '../screens/ImageDisplayScreen';
import AboutSettingScreen from '../screens/AboutSettingScreen';
import TermsAndUseScreen from '../screens/TermsAndUseScreen';
import FaqDisScreen from '../screens/FaqDisScreen';
import PrivacyPolicyDisScreen from '../screens/PrivacyPolicyDisScreen';
import EditPostScreen from '../screens/EditPostScreen';
import CategoryWiseUserScreen from '../screens/CategoryWiseUserScreen';
import messaging from '@react-native-firebase/messaging';
import AsyncStorage from '@react-native-async-storage/async-storage';
import SettingScreen from '../screens/SettingScreen';
import {Notifier, Easing} from 'react-native-notifier';
import SignupGmailScreen from '../screens/SignupGmailScreen';
import {
  NOTI_NEW_POST_SUB,
  NOTI_NEW_PROFILE_FOLLOW,
  NOTI_NEW_PROFILE_SUB,
  NOTI_POST_CAPTION,
  NOTI_POST_COMMENT,
  NOTI_POST_LIKE,
  NOTI_POST_TAG,
  NOTI_PROFILE_BIO,
  NOTI_STORY_CAPTION,
} from '../constants/Constants';
import TagPeopleScreen from '../screens/TagPeopleScreen';
import TagPeopleListScreen from '../screens/TagPeopleListScreen';
import SingleStoryStatusScreen from '../screens/SingleStoryStatusScreen';
import ServerConnector from '../utils/ServerConnector';
import AppIntroSliderScreen from '../screens/AppIntroSliderScreen';
import dynamicLinks from '@react-native-firebase/dynamic-links';
import SignupAppleScreen from '../screens/SignupAppleScreen';
import DisableAccountScreen from '../screens/DisableAccountScreen';
import SignInNScreen from '../screens/SignInNScreen';
import SignupNScreen from '../screens/SignupNScreen';
import SinglePostNScreen from '../screens/SinglePostNScreen';
import TermsAndConditionScreen from '../screens/TermsAndConditionScreen';
import FlashMessage from 'react-native-flash-message';
import VideoContentScreen from '../screens/VideoContentScreen';
import UnlockSinglePostScreen from '../screens/UnlockSinglePostScreen';
import AddPostSuccessScreen from '../screens/AddPostSuccessScreen';
import NotificationScreen from '../screens/NotificationScreen';
import QuickSignInScreen from '../screens/2.0/QuickSignInScreen';
import QuickSignInOTPScreen from '../screens/2.0/QuickSignInOTPScreen';
import QuickSignUpScreen from '../screens/2.0/QuickSignUpScreen';
import QuickSignUpPersonalizeScreen from '../screens/2.0/QuickSignUpPersonalizeScreen';
import PlaylistScreen from '../screens/PlaylistScreen';
import PostingJourneyScreen from '../screens/2.0/PostingJourneyScreen';
import LoginOldFlowScreen from '../screens/LoginOldFlowScreen';
import ViewTransactionsScreen from '../screens/ViewTransactionsScreen';
import SearchResultMainScreen from '../screens/SearchResultMainScreen';
import UserPersonalizeScreen from '../screens/2.0/UserPersonalizeScreen';
import PlaylistEpisodeScreen from '../screens/PlaylistEpisodeScreen';
import ShareYourProfileScreen from '../screens/ShareYourProfileScreen';
import PlaylistShowScreen from '../screens/PlaylistShowScreen';
import PlaylistSeasonScreen from '../screens/PlaylistSeasonScreen';
import PlaylistSeasonEpisodeScreen from '../screens/PlaylistSeasonEpisodeScreen';
import TempHomeScreen from '../screens/2.0/TempHomeScreen';
import NotificationGuideSelectionScreen from '../screens/2.0/NotificationGuideSelectionScreen';
import withOrientationLock from '../components/common/OrientationWrapper';
// import ScreenGuardModule from 'react-native-screenguard';

const Stack = createNativeStackNavigator();
const useMount = func => useEffect(() => func(), []);

// Helper function to create orientation-locked screen components
const createOrientationLockedScreen = (Component, allowRotation = false) => {
  return props => {
    const WrappedComponent = withOrientationLock(Component, allowRotation);
    return <WrappedComponent {...props} />;
  };
};

const AppNavigator = () => {
  const {
    changeNewNotficationCame,
    changeNewNotficationTypeList,
    newNotificationTypeList,
  } = useContext(AppStateContext);
  const {changeNotificationRefresh} = useContext(PageRefreshContext);
  const MyTheme = {
    ...DefaultTheme,
    colors: {
      ...DefaultTheme.colors,
      background: Colors.backgroundColor,
    },
  };
  useMount(() => {
    const getUrlAsync = async () => {
      // Get the deep link used to open the app
      const initialUrl = await Linking.getInitialURL();

      // The setTimeout is just for testing purpose
      setTimeout(() => {
        // console.log("initialUrl2", initialUrl)
      }, 1000);
    };

    getUrlAsync();
  });
  const navigation = useNavigation();
  useEffect(() => {
    requestUserPermission();
    getNotificationService();
  }, []);

  useEffect(() => {
    const unsubscribe = messaging().onMessage(async remoteMessage => {
      // Alert.alert('A new FCM message arrived!', JSON.stringify(remoteMessage));

      if (remoteMessage.hasOwnProperty('data')) {
        let resData = remoteMessage.data;
        if (resData.hasOwnProperty('type')) {
          changeNewNotficationCame(true);
          // console.log("remoteMessage List", newNotificationTypeList)
          let dataType = newNotificationTypeList;
          dataType.push(resData.type);
          changeNewNotficationTypeList(dataType);
        }
      }
      Notifier.showNotification({
        title: remoteMessage.notification.title,
        description: remoteMessage.notification.body,
        duration: 5000,
        showAnimationDuration: 800,
        componentProps: {
          titleStyle: {color: '#000'},
        },
        showEasing: Easing.bounce,
        // onHidden: () => console.log('Hidden'),
        onPress: () => redirectTospecificScreen(remoteMessage.data),
        hideOnPress: true,
        swipeEnabled: true,
      });
      // redirectTospecificScreen(remoteMessage.data)
    });

    return unsubscribe;
  }, [newNotificationTypeList]);

  //FCM Integration
  async function requestUserPermission() {
    const authStatus = await messaging().requestPermission();
    const enabled =
      authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
      authStatus === messaging.AuthorizationStatus.PROVISIONAL;

    if (enabled) {
      // console.log('Authorization status:', authStatus);
      getFcmTocken();
    }
  }
  const getFcmTocken = async () => {
    let fcmToken = await AsyncStorage.getItem('_FCM_TOKEN_A');
    // console.log(fcmToken, "the old token");
    if (!fcmToken) {
      try {
        const fcmToken = await messaging().getToken();
        // console.log(fcmToken, "the New token");
        AsyncStorage.setItem('_FCM_TOKEN_A', fcmToken);
      } catch (error) {
        console.log(error, 'error raised in fcm token');
      }
    }
  };

  const redirectTospecificScreen = data => {
    if (data.hasOwnProperty('type')) {
      if (data.type == NOTI_NEW_PROFILE_SUB) {
        let profile_seq = data.profile_seq;
        navigation.dispatch(
          CommonActions.navigate({
            name: 'OthersProfileScreen',
            params: {
              profileSeq: profile_seq,
            },
          }),
        );
      } else if (data.type == NOTI_NEW_POST_SUB) {
        let post_seq = data.post_seq;
        navigation.dispatch(
          CommonActions.navigate({
            name: 'SinglePostScreen',
            params: {
              postSeq: post_seq,
            },
          }),
        );
      } else if (data.type == NOTI_NEW_PROFILE_FOLLOW) {
        let profile_seq = data.profile_seq;
        navigation.dispatch(
          CommonActions.navigate({
            name: 'OthersProfileScreen',
            params: {
              profileSeq: profile_seq,
            },
          }),
        );
      } else if (data.type == NOTI_POST_LIKE) {
        let post_seq = data.post_seq;
        navigation.dispatch(
          CommonActions.navigate({
            name: 'SinglePostScreen',
            params: {
              postSeq: post_seq,
            },
          }),
        );
      } else if (data.type == NOTI_POST_COMMENT) {
        let post_seq = data.post_seq;
        navigation.dispatch(
          CommonActions.navigate({
            name: 'SinglePostScreen',
            params: {
              postSeq: post_seq,
            },
          }),
        );
      } else if (data.type == NOTI_POST_TAG) {
        let post_seq = data.post_seq;
        navigation.dispatch(
          CommonActions.navigate({
            name: 'SinglePostScreen',
            params: {
              postSeq: post_seq,
            },
          }),
        );
      } else if (data.type == NOTI_POST_CAPTION) {
        let post_seq = data.post_seq;
        navigation.dispatch(
          CommonActions.navigate({
            name: 'SinglePostScreen',
            params: {
              postSeq: post_seq,
            },
          }),
        );
      } else if (data.type == NOTI_PROFILE_BIO) {
        let profile_seq = data.post_seq;
        navigation.dispatch(
          CommonActions.navigate({
            name: 'OthersProfileScreen',
            params: {
              profileSeq: profile_seq,
            },
          }),
        );
      } else if (data.type == NOTI_STORY_CAPTION) {
        let post_seq = data.post_seq;
        navigation.dispatch(
          CommonActions.navigate({
            name: 'SingleStoryStatusScreen',
            params: {
              storySeq: post_seq,
            },
          }),
        );
      } else {
        navigation.dispatch(
          CommonActions.navigate({
            name: 'HomeScreen',
          }),
        );
      }
    }
  };
  const [aState, setAppState] = useState(AppState.currentState);
  useEffect(() => {
    const appStateListener = AppState.addEventListener(
      'change',
      nextAppState => {
        // console.log('Next AppState is: ', nextAppState);
        if (nextAppState == 'active') {
          getNotificationService();
          // console.log("Active State");
          // changeNewNotficationCame(true);
          // console.log("appStateListener List", newNotificationTypeList)
          // let dataType = newNotificationTypeList;
          // dataType.push("POST_TAG");
          // changeNewNotficationTypeList(dataType);
          // ScreenGuardModule.register(
          //             //insert any hex color you want here, default black if null or empty
          //            '#000000',
          //        _ => {
          //      // .....do anything you want after the screenshot
          //           },)
        } else {
          // ScreenGuardModule.unregister();
        }

        setAppState(nextAppState);
      },
    );
    return () => {
      appStateListener?.remove();
    };
  }, []);
  function getNotificationService() {
    let hashMap = {
      _action_code: '11:GET_NOTIFICATION_STATUS',
    };
    let connector = new ServerConnector();
    connector.postData(
      hashMap,
      data => {
        // success method
        let notiCame = false;
        let notList = [];
        data.data.map(obj => {
          if (obj.status == 'YES') {
            notiCame = true;
            let notiType = givingNotiTypeByViewType(obj.view_tab);
            notList.push(notiType);
          }
        });
        if (notiCame) {
          changeNewNotficationCame(true);
          let dataType = [...newNotificationTypeList];
          dataType.concat(notList);
          changeNewNotficationTypeList(notList);
        } else {
          changeNewNotficationTypeList([]);
        }
      },
      (errorCode, errorMessage, data) => {
        // failure method
      },
    );
  }
  const givingNotiTypeByViewType = viewType => {
    let notiType = '';
    if (viewType == 'SUBSCRIBE') {
      notiType = NOTI_NEW_PROFILE_SUB;
    } else if (viewType == 'LIKE') {
      notiType = NOTI_POST_LIKE;
    } else if (viewType == 'COMMENTS') {
      notiType = NOTI_POST_COMMENT;
    } else if (viewType == 'FOLLOW') {
      notiType = NOTI_NEW_PROFILE_FOLLOW;
    } else if (viewType == 'TAG') {
      notiType = NOTI_POST_TAG;
    }
    return notiType;
  };

  const handleDynamicLink = link => {
    dynamicLinkRedirection(link);
  };
  useEffect(() => {
    // console.log("UnSubscribe Log")
    const unsubscribe = dynamicLinks().onLink(handleDynamicLink);
    // When the component is unmounted, remove the listener
    return () => unsubscribe();
  }, []);
  useEffect(() => {
    dynamicLinks()
      .getInitialLink()
      .then(link => {
        dynamicLinkRedirection(link);
      });
  }, []);

  const dynamicLinkRedirection = dynamicUrl => {
    if (dynamicUrl == null) return;
    // console.log("dynamicUrl", dynamicUrl)
    const routes = navigation.getState()?.routes;
    // console.log("routes", routes)
    if (routes != undefined) {
      if (dynamicUrl.hasOwnProperty('url')) {
        // console.log("dynamicUrl IN", dynamicUrl.url)
        let rUrl = dynamicUrl.url;
        let params = rUrl.substring(rUrl.indexOf('?') + 1);
        if (rUrl.startsWith('https://sotrue.co.in/post')) {
          navigation.dispatch(
            CommonActions.navigate({
              name: 'SinglePostScreen',
              params: {
                postSeq: params,
              },
            }),
          );
        } else if (rUrl.startsWith('https://sotrue.co.in/profile')) {
          navigation.dispatch(
            CommonActions.navigate({
              name: 'OthersProfileScreen',
              params: {
                profileSeq: params,
              },
            }),
          );
        } else {
          navigation.dispatch(
            CommonActions.navigate({
              name: 'HomeScreen',
            }),
          );
        }
        // console.log("params", params)
      }
    }
  };
  useEffect(() => {
    const listener = Appearance.addChangeListener(colorTheme => {
      // console.log("colorTheme", colorTheme)
    });
    return () => {
      listener;
    };
  }, []);

  return (
    <>
      {/* <SafeAreaProvider>
             <StatusBar
                animated={true}
                barStyle={'dark-content'}
                hidden={false}
                backgroundColor={Colors.statusBarColor}
            /> */}
      <Stack.Navigator
        screenOptions={{
          headerShown: false,
          cardStyle: {backgroundColor: '#111111'},
          animationEnabled: Platform.select({
            ios: true,
            android: false,
          }),
        }}>
        <Stack.Group>
          {/* SplashScreen without orientation wrapper to prevent freezing */}
          <Stack.Screen name="SplashScreen">
            {props => <SplashScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen
            name="LoginScreen"
            component={createOrientationLockedScreen(LoginScreen)}
          />
          <Stack.Screen
            name="ForgotPasswordScreen"
            component={createOrientationLockedScreen(ForgotPasswordScreen)}
          />
          <Stack.Screen
            name="ForgotVerifyEmailScreen"
            component={createOrientationLockedScreen(ForgotVerifyEmailScreen)}
          />
          <Stack.Screen
            name="ForgotResetPassScreen"
            component={createOrientationLockedScreen(ForgotResetPassScreen)}
          />
          <Stack.Screen
            name="ForgotResetPassSuccessScreen"
            component={createOrientationLockedScreen(
              ForgotResetPassSuccessScreen,
            )}
          />
          <Stack.Screen
            name="SignupScreen"
            component={createOrientationLockedScreen(SignupScreen)}
          />
          <Stack.Screen
            name="TempHomeScreen"
            component={createOrientationLockedScreen(TempHomeScreen)}
          />
          <Stack.Screen
            name="HomeScreen"
            component={createOrientationLockedScreen(BottomNavigator)}
          />
          <Stack.Screen
            name="SignupOtpScreen"
            component={createOrientationLockedScreen(SignupOtpScreen)}
          />
          <Stack.Screen
            name="SignupGmailScreen"
            component={createOrientationLockedScreen(SignupGmailScreen)}
          />
          <Stack.Screen
            name="SignupAppleScreen"
            component={createOrientationLockedScreen(SignupAppleScreen)}
          />
          {/* 2.0 */}
          <Stack.Screen
            name="QuickSignInScreen"
            component={createOrientationLockedScreen(QuickSignInScreen)}
          />
          <Stack.Screen
            name="QuickSignInOTPScreen"
            component={createOrientationLockedScreen(QuickSignInOTPScreen)}
          />
          <Stack.Screen
            name="QuickSignUpScreen"
            component={createOrientationLockedScreen(QuickSignUpScreen)}
          />
          <Stack.Screen
            name="QuickSignUpPersonalizeScreen"
            component={createOrientationLockedScreen(
              QuickSignUpPersonalizeScreen,
            )}
          />
          <Stack.Screen
            name="LoginOldFlowScreen"
            component={createOrientationLockedScreen(LoginOldFlowScreen)}
          />
          <Stack.Screen
            name="NotificationGuideSelectionScreen"
            component={createOrientationLockedScreen(
              NotificationGuideSelectionScreen,
            )}
          />
        </Stack.Group>
        <Stack.Group>
          {/*screenOptions={{ presentation: 'modal' }} */}
          <Stack.Screen
            name="StoryStatusScreen"
            component={createOrientationLockedScreen(StoryStatusScreen)}
          />
          <Stack.Screen
            name="VideoDisplayScreen"
            component={createOrientationLockedScreen(VideoDisplayScreen)}
          />
          <Stack.Screen
            name="OthersProfileScreen"
            component={createOrientationLockedScreen(OthersProfileScreen)}
          />
          <Stack.Screen
            name="AccountSettingsScreen"
            component={createOrientationLockedScreen(AccountSettingsScreen)}
          />
          <Stack.Screen
            name="FollowingScreen"
            component={createOrientationLockedScreen(FollowingScreen)}
          />
          <Stack.Screen
            name="FollowersScreen"
            component={createOrientationLockedScreen(FollowersScreen)}
          />
          <Stack.Screen
            name="CommentScreen"
            component={createOrientationLockedScreen(CommentScreen)}
          />
          <Stack.Screen
            name="SearchResultScreen"
            component={createOrientationLockedScreen(SearchResultScreen)}
            options={{animation: 'slide_from_right'}}
          />
          <Stack.Screen
            name="EditProfileScreen"
            component={createOrientationLockedScreen(EditProfileScreen)}
          />
          <Stack.Screen
            name="AccountInfoScreen"
            component={createOrientationLockedScreen(AccountInfoScreen)}
          />
          <Stack.Screen
            name="CameraScreen"
            component={createOrientationLockedScreen(CameraScreen)}
          />
          <Stack.Screen
            name="CaptureMediaDisplayScreen"
            component={createOrientationLockedScreen(CaptureMediaDisplayScreen)}
          />
          <Stack.Screen
            name="BlockedAccountScreen"
            component={createOrientationLockedScreen(BlockedAccountScreen)}
          />
          <Stack.Screen
            name="PrivacySafetyScreen"
            component={createOrientationLockedScreen(PrivacySafetyScreen)}
          />
          <Stack.Screen
            name="AddStoryScreen"
            component={createOrientationLockedScreen(AddStoryScreen)}
          />
          <Stack.Screen
            name="SubscriptionScreen"
            component={createOrientationLockedScreen(SubscriptionScreen)}
          />
          <Stack.Screen
            name="MySubscribersScreen"
            component={createOrientationLockedScreen(MySubscribersScreen)}
          />
          <Stack.Screen
            name="VerificationsScreen"
            component={createOrientationLockedScreen(VerificationsScreen)}
          />
          <Stack.Screen
            name="SuccessfullVerificationScreen"
            component={createOrientationLockedScreen(
              SuccessfullVerificationScreen,
            )}
          />
          <Stack.Screen
            name="ReferAndEarnScreen"
            component={createOrientationLockedScreen(ReferAndEarnScreen)}
          />
          <Stack.Screen
            name="ReferralHistoryScreen"
            component={createOrientationLockedScreen(ReferralHistoryScreen)}
          />
          <Stack.Screen
            name="MySubscriptionsScreen"
            component={createOrientationLockedScreen(MySubscriptionsScreen)}
          />
          <Stack.Screen
            name="AddPostScreen"
            component={createOrientationLockedScreen(AddPostScreen)}
          />
          <Stack.Screen
            name="RestrictedAccountScreen"
            component={createOrientationLockedScreen(RestrictedAccountScreen)}
          />
          <Stack.Screen
            name="BookmarkListScreen"
            component={createOrientationLockedScreen(BookmarkListScreen)}
          />
          <Stack.Screen
            name="NotificationSettingsScreen"
            component={createOrientationLockedScreen(
              NotificationSettingsScreen,
            )}
          />
          <Stack.Screen
            name="SinglePostScreen"
            component={createOrientationLockedScreen(SinglePostScreen)}
          />
          <Stack.Screen
            name="SelectDataFieldScreen"
            component={createOrientationLockedScreen(SelectDataFieldScreen)}
          />
          <Stack.Screen
            name="ImageDisplayScreen"
            component={createOrientationLockedScreen(ImageDisplayScreen)}
          />
          <Stack.Screen
            name="AboutSettingScreen"
            component={createOrientationLockedScreen(AboutSettingScreen)}
          />
          <Stack.Screen
            name="TermsAndUseScreen"
            component={createOrientationLockedScreen(TermsAndUseScreen)}
          />
          <Stack.Screen
            name="PrivacyPolicyDisScreen"
            component={createOrientationLockedScreen(PrivacyPolicyDisScreen)}
          />
          <Stack.Screen
            name="FaqDisScreen"
            component={createOrientationLockedScreen(FaqDisScreen)}
          />
          <Stack.Screen
            name="EditPostScreen"
            component={createOrientationLockedScreen(EditPostScreen)}
          />
          <Stack.Screen
            name="CategoryWiseUserScreen"
            component={createOrientationLockedScreen(CategoryWiseUserScreen)}
          />
          <Stack.Screen
            name="SettingScreen"
            component={createOrientationLockedScreen(SettingScreen)}
          />
          <Stack.Screen
            name="TagPeopleScreen"
            component={createOrientationLockedScreen(TagPeopleScreen)}
          />
          <Stack.Screen
            name="TagPeopleListScreen"
            component={createOrientationLockedScreen(TagPeopleListScreen)}
          />
          <Stack.Screen
            name="SingleStoryStatusScreen"
            component={createOrientationLockedScreen(SingleStoryStatusScreen)}
          />
          <Stack.Screen
            name="AppIntroSliderScreen"
            component={createOrientationLockedScreen(AppIntroSliderScreen)}
          />
          <Stack.Screen
            name="DisableAccountScreen"
            component={createOrientationLockedScreen(DisableAccountScreen)}
          />
          <Stack.Screen
            name="SignInNScreen"
            component={createOrientationLockedScreen(SignInNScreen)}
          />
          <Stack.Screen
            name="SignupNScreen"
            component={createOrientationLockedScreen(SignupNScreen)}
          />
          <Stack.Screen
            name="SinglePostNScreen"
            component={createOrientationLockedScreen(SinglePostNScreen)}
          />
          <Stack.Screen
            name="TermsAndConditionScreen"
            component={createOrientationLockedScreen(TermsAndConditionScreen)}
          />
          {/* VideoContentScreen keeps its own orientation logic */}
          <Stack.Screen name="VideoContentScreen">
            {props => <VideoContentScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen
            name="UnlockSinglePostScreen"
            component={createOrientationLockedScreen(UnlockSinglePostScreen)}
          />
          <Stack.Screen
            name="AddPostSuccessScreen"
            component={createOrientationLockedScreen(AddPostSuccessScreen)}
          />
          <Stack.Screen
            name="NotificationScreen"
            component={createOrientationLockedScreen(NotificationScreen)}
          />
          <Stack.Screen
            name="PlaylistScreen"
            component={createOrientationLockedScreen(PlaylistScreen)}
          />
          <Stack.Screen
            name="PostingJourneyScreen"
            component={createOrientationLockedScreen(PostingJourneyScreen)}
          />
          <Stack.Screen
            name="ViewTransactionsScreen"
            component={createOrientationLockedScreen(ViewTransactionsScreen)}
          />
          <Stack.Screen
            name="SearchResultMainScreen"
            component={createOrientationLockedScreen(SearchResultMainScreen)}
          />
          <Stack.Screen
            name="UserPersonalizeScreen"
            component={createOrientationLockedScreen(UserPersonalizeScreen)}
          />
          <Stack.Screen
            name="PlaylistEpisodeScreen"
            component={createOrientationLockedScreen(PlaylistEpisodeScreen)}
          />
          <Stack.Screen
            name="ShareYourProfileScreen"
            component={createOrientationLockedScreen(ShareYourProfileScreen)}
          />
          <Stack.Screen
            name="PlaylistShowScreen"
            component={createOrientationLockedScreen(PlaylistShowScreen)}
          />
          <Stack.Screen
            name="PlaylistSeasonScreen"
            component={createOrientationLockedScreen(PlaylistSeasonScreen)}
          />
          <Stack.Screen
            name="PlaylistSeasonEpisodeScreen"
            component={createOrientationLockedScreen(
              PlaylistSeasonEpisodeScreen,
            )}
          />
        </Stack.Group>
      </Stack.Navigator>
      {/* </SafeAreaProvider> */}
      <FlashMessage position="top" />
    </>
  );
};

export default AppNavigator;

const styles = StyleSheet.create({});
