import React, {useState, useContext} from 'react';
import {
  View,
  StyleSheet,
  Modal,
  TextInput,
  Image,
  Dimensions,
  TouchableOpacity,
} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import Svg, {Polygon} from 'react-native-svg';
import useSTheme from '../../theme/useSTheme';
import EntutoTextView from './EntutoTextView';
import PrimaryButton from './PrimaryButton';
import Eva from '../../assets/Images/Eva.png';
import Neil from '../../assets/Images/Neil.png';

import ThumbUpOutlined from '../../assets/Thumb_outlined_new/Thumb-Up-Outlined.png';
import ThumbUpFilled from '../../assets/Images/icon/Thumb-Up-Filled.png';
import ThumbDownOutlined from '../../assets/Thumb_outlined_new/Thumb-Down-Outlined.png';
import ThumbDownFilled from '../../assets/Images/icon/Thumb-Down-Filled.png';
import {AppStateContext} from '../../index';

const {width: screenWidth, height: screenHeight} = Dimensions.get('window');

// Triangle dimensions (W=40, H=60)
const TAIL_WIDTH = 40;
const TAIL_HEIGHT = 60;

const OnboardingFeedbackPopup = ({render = false, onSubmit = () => {}}) => {
  const theme = useSTheme();
  const insets = useSafeAreaInsets();
  const {selectedGuide} = useContext(AppStateContext);
  console.log('selectedGuide', selectedGuide);
  const [feedbackText, setFeedbackText] = useState();
  const [selectedThumb, setSelectedThumb] = useState('up'); // Default to thumbs up

  const handleThumbPress = type => {
    setSelectedThumb(selectedThumb === type ? null : type);
  };

  const handleSubmit = () => {
    onSubmit({text: feedbackText, feedback: selectedThumb});
  };

  if (!render) return null;

  return (
    <Modal
      visible={render}
      transparent={true}
      animationType="fade"
      statusBarTranslucent={true}>
      <View style={styles.backdrop}>
        <View style={styles.contentContainer}>
          {/* Bottom‐right guide image */}
          <View style={[styles.imageContainer, {bottom: -insets.bottom}]}>
            <Image
              source={selectedGuide == 'eva' ? Eva : Neil}
              style={styles.guideImage}
              resizeMode="cover"
            />
          </View>

          {/* Wrapper for bubble + its filled‐color tail */}
          <View
            style={[
              styles.feedbackWrapper,
              {
                bottom:
                  screenHeight * 0.35 + // image height
                  30 + // gap between image & bubble
                  insets.bottom, // safe‐area inset
              },
            ]}>
            {/* The speech bubble */}
            <View
              style={[
                styles.speechBubble,
                {
                  backgroundColor: theme.colors.backgroundColor,
                  borderColor: theme.colors.backgroundColor,
                },
              ]}>
              <EntutoTextView
                style={[
                  styles.questionText,
                  {color: theme.colors.primaryTextColor},
                ]}>
                {selectedGuide == 'eva'
                  ? 'Hey you! Real quick, could the sign up process be any cooler than that?'
                  : 'We both know this was an awesome sign up experience right?'}
              </EntutoTextView>

              <View style={styles.thumbsContainer}>
                <TouchableOpacity
                  style={styles.thumbButton}
                  onPress={() => handleThumbPress('up')}>
                  <Image
                    source={
                      selectedThumb === 'up' ? ThumbUpFilled : ThumbUpOutlined
                    }
                    style={styles.thumbIcon}
                  />
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.thumbButton}
                  onPress={() => handleThumbPress('down')}>
                  <Image
                    source={
                      selectedThumb === 'down'
                        ? ThumbDownFilled
                        : ThumbDownOutlined
                    }
                    style={styles.thumbIcon}
                  />
                </TouchableOpacity>
              </View>

              <TextInput
                style={[
                  styles.textInput,
                  {
                    borderColor: theme.colors.primaryColor,
                    color: theme.colors.inputTextColor,
                  },
                ]}
                value={feedbackText}
                onChangeText={setFeedbackText}
                multiline
                numberOfLines={3}
                placeholder="I really want to know your thoughts on this even though it's optional!"
                placeholderTextColor={theme.colors.inputPlaceholderColor}
              />

              <View style={styles.buttonContainer}>
                <PrimaryButton
                  label="Submit"
                  onPress={handleSubmit}
                  style={styles.submitButton}
                />
              </View>
            </View>

            {/* Filled triangle “tail” under the bubble, using theme background color */}
            <View style={styles.tailContainer}>
              <Svg width={TAIL_WIDTH} height={TAIL_HEIGHT}>
                <Polygon
                  points={`0,0 ${TAIL_WIDTH},0 ${
                    TAIL_WIDTH / 2
                  },${TAIL_HEIGHT}`}
                  fill={theme.colors.backgroundColor}
                />
              </Svg>
            </View>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  backdrop: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
  },
  contentContainer: {
    flex: 1,
    position: 'relative',
  },

  /* Bottom‐right guide image */
  imageContainer: {
    position: 'absolute',
    right: 5,
    width: screenWidth * 0.5,
    height: screenHeight * 0.35,
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
  guideImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },

  /*
   * feedbackWrapper is centered horizontally by spanning from 20% → 80% of screen width.
   * `alignItems: 'center'` ensures its children (bubble + tail) are centered inside it.
   */
  feedbackWrapper: {
    position: 'absolute',
    left: '10%',
    right: '10%',
    alignItems: 'center',
  },

  /* The speech bubble box (background & border now theme.colors.backgroundColor) */
  speechBubble: {
    position: 'relative',
    borderRadius: 10,
    padding: 20,
    borderWidth: 1,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.3,
    shadowRadius: 5,
    elevation: 8,
    width: '100%',
    zIndex: 1,
  },

  questionText: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 15,
    lineHeight: 22,
  },
  textInput: {
    borderWidth: 1,
    // borderRadius: 10,
    padding: 12,
    fontSize: 14,
    textAlignVertical: 'top',
    minHeight: 80,
    marginBottom: 15,
    width: '100%', // ensure TextInput takes full width
  },
  buttonContainer: {
    width: '100%', // ensure container takes full width
    // alignItems removed to allow stretch
  },
  submitButton: {
    width: '100%', // make button take full width of its container
  },
  thumbContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    marginBottom: 15,
  },
  thumbButton: {
    padding: 8,
    height: '100%',
  },
  thumbsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 15,
    height: 100,
    gap: 20,
  },
  thumbIcon: {
    width: 56,
    height: 56,
    resizeMode: 'contain',
  },

  /**
   * Position the filled triangle so that its top edge (base of the triangle)
   * sits flush under the bubble's bottom. Because the bubble's border is the
   * same theme color, they blend seamlessly.
   */
  tailContainer: {
    alignSelf: 'flex-end',
    right: 20,
    marginTop: -1, // pull it up slightly so the base touches the bubble’s bottom edge
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.3,
    shadowRadius: 5,
    elevation: 8,
    zIndex: 1,
  },
});

export default OnboardingFeedbackPopup;
