##PostCard.js

PostCard.js is a critical and very large component responsible for rendering a single post in the application.
It's highly versatile and handles a wide array of functionalities, including:

- Content Display: It can render different types of media, such as images and videos (including fullscreen
  reels).
- User Interactions: It manages user actions like liking, commenting, bookmarking, and sharing posts. It also
  includes logic for double-tap-to-like and handling various reactions (emotions).
- Business Logic: It contains complex logic for handling different post states, such as locked/unlocked (for
  paid content), blocked posts, and subscribed content. It also manages user following status.
- Data Fetching: It directly makes API calls to the backend (ServerConnector) to submit likes, bookmarks,
  views, and other user interactions.
- UI Variations: The component's appearance and available actions change based on numerous props like
  isMyProfile, isForceUnlock, fullScreen, and showVideoContent.

Component Relationships

To determine the parent and child components, I will now search the codebase.

- Child Components: These are the components imported and used directly within PostCard.js. My analysis of the
  file shows it uses:

  - EntutoTextView, ProgressiveImage, LikeBtnComponent, BookmarkBtnComponent, BottomSheetSuccessMsg,
    ConfirmationPopup, CustomSnackbar, DescriptionCaptionStyle, SharePostProfileFeature, SharePostIcon (from
    ../common/)
  - UnlockPostActionView, ThreeDotMenuActionView, CommentComponent, ResolutionVideoSwitch, FullScreenPlayer
    (from ./)
  - UpdateUserLocationComponent (from ../profile/)
  - ReelProgressBar (from ../videoContents/)

- Parent Components: To find which components use PostCard, I need to search the project.
  ╭────────────────────────────────────────────────────────────────────────────────────────────────────────╮
  │ ✔ SearchText 'PostCard' in \*_/_.js │
  │ │
  │ Found 138 matche(s) │
  ╰────────────────────────────────────────────────────────────────────────────────────────────────────────╯
  ✦ The search results confirm that PostCard is a widely used component. Here are its parent components:

- components\videoContents\Reels.js
- screens\BookmarkListScreen.js
- screens\HomeScreen.js
- screens\MySubscriptionsScreen.js
- screens\SinglePostScreen.js
- screens\UnlockSinglePostScreen.js
- screens\VideoContentScreen.js
- screens\ViewTransactionsScreen.js
- components\profile\ProfileComponent.js (commented out, but indicates usage)

Weirdness Report

The most striking aspect of PostCard.js is its extreme size and complexity. A single component file of over
4,000 lines is a significant "code smell." This leads to several issues:

1.  Single Responsibility Principle Violation: This component does far too much. It handles UI, state management,
    business logic, and direct API calls. This makes it incredibly difficult to understand, maintain, and test.
2.  Readability and Maintainability: The sheer number of state variables (useState), effects (useEffect), and
    functions within a single file makes it very hard to follow the logic. For example, there are over 50
    useState hooks.
3.  Performance: While React.memo is used, the sheer complexity and number of state variables and props can lead
    to unnecessary re-renders. The component's performance could be a concern, especially in long lists.
4.  Hardcoded Values: There are several hardcoded strings and values, such as error messages and API action codes
    (e.g., '11:SUBMIT_POST_LIKE'). These should be centralized in constants.
5.  Redundant Logic: There appears to be some duplicated or similar logic in different parts of the component,
    especially around handling different post types and user states.
6.  Prop Drilling: The component accepts a large number of props, and it's likely that some of these are passed
    down through multiple layers of components. The postCardClick prop is a good example of an event handler that
    is passed down and used for various purposes.

In short, PostCard.js is a "god component" that would greatly benefit from being broken down into smaller,
more focused components with custom hooks to handle the complex logic. This would improve readability,
maintainability, and testability.
